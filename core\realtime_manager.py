"""
阿里千问qwen-omni-turbo-realtime API Manager

管理与阿里千问qwen-omni-turbo-realtime API的WebSocket连接，处理事件收发和会话管理。
提供强化的连接稳定性和自动重连机制。

关键功能：
1. WebSocket连接管理和自动重连
2. 事件收发处理
3. 会话生命周期管理
4. 音频数据流处理
5. 连接健康监控
6. 直接音频通道支持 (新增)
"""

import asyncio
import json
import websockets
from datetime import datetime
from loguru import logger
from typing import Optional, Dict, Any, Callable, List
from dataclasses import dataclass
from urllib.parse import urlencode

from .config import get_realtime_config, get_qwen_voice_mapping, settings
from .exceptions import WebSocketConnectionError
from .retry_utils import retry_with_backoff


@dataclass
class DirectAudioConnection:
    """直接音频连接数据结构"""
    session_id: str
    websocket: Any  # WebSocket connection
    last_activity: datetime
    audio_format: str = "pcm16"
    sample_rate: int = 24000
    
    def is_active(self) -> bool:
        """检查连接是否仍然活跃 - 修复版本，支持标准WebSocket"""
        try:
            # 优先检查FastAPI WebSocket的连接状态
            if hasattr(self.websocket, 'client_state'):
                return self.websocket.client_state.name == 'CONNECTED'
            # 检查标准WebSocket的连接状态 (前端WebSocket对象)
            elif hasattr(self.websocket, 'readyState'):
                # WebSocket.OPEN = 1
                return self.websocket.readyState == 1
            # 检查其他可能的状态属性
            elif hasattr(self.websocket, 'state'):
                return str(self.websocket.state).upper() in ['CONNECTED', 'OPEN']
            else:
                logger.error(f"⚠️ Unable to determine WebSocket state for session {self.session_id}, assuming active")
                return True
        except Exception as e:
            logger.error(f"⚠️ Error checking WebSocket state for session {self.session_id}: {e}")
            return False


class RealtimeManager:
    def __init__(self, api_key: str = None, model: str = None, base_url: str = None):
        """
        初始化阿里千问qwen-omni-turbo-realtime管理器
        
        Args:
            api_key: 阿里千问 API密钥 (可选，默认从配置获取)
            model: 使用的模型名称 (固定为qwen-omni-turbo-realtime)
            base_url: API基础URL (可选，默认从配置获取)
        """
        # 从配置获取默认值
        config = get_realtime_config()
        
        self.api_key = api_key or config["api_key"]
        self.model = "qwen-omni-turbo-realtime"  # 固定模型名称
        self.base_url = base_url or config["base_url"]
        self.ws_endpoint = config["ws_endpoint"]
        
        self.websocket = None
        self.is_connected = False
        self.is_session_active = False
        self.session_id = None
        self.current_session_config = None
        self.last_error = None
        
        # 🎯 修复：添加用户会话ID跟踪
        self.current_session_id = None  # 用户WebSocket会话ID
        
        # 连接管理
        self.connection_attempts = 0
        self.max_connection_attempts = 10
        self.reconnect_delay = 4.0  # 初始重连延迟（秒）
        self.max_reconnect_delay = 30.0  # 最大重连延迟（秒）
        self.reconnect_enabled = True
        
        # 任务管理
        self.message_task = None
        self.heartbeat_task = None
        self.reconnect_task = None
        
        # 事件处理器
        self.event_handlers: Dict[str, List[Callable]] = {}
        
        # 连接健康监控
        self.last_ping_time = None
        self.last_pong_time = None
        self.ping_failures = 0
        self.max_ping_failures = 3
        
        # 音频处理状态 
        self.current_audio_response_id = None
        self.is_outputting_audio = False
        self.audio_output_buffer = []  # 缓存音频数据
        
        # 音频中断机制
        self.audio_interrupted = False
        self.speech_detected = False
        self.last_speech_detection_time = None
        
        # 🎯 新增：直接WebSocket连接管理
        self.direct_connections: Dict[str, DirectAudioConnection] = {}
        self.direct_audio_enabled = True  # 是否启用直接音频通道
        
        # 🎯 修复重复消息：响应转录累积字典
        self.response_transcripts: Dict[str, str] = {}  # response_id -> 累积的转录文本
        
        # 🎯 新增：响应状态跟踪（防止API冲突）
        self.is_response_active = False  # 跟踪是否有活跃响应
        
        # 🎯 双轨ASR支持：音频分发器
        self.audio_splitter = None
        self.audio_splitter_initialized = False
        
        # 🎯 Paraformer降级机制
        self._paraformer_fallback = False
        self._paraformer_error_count = 0
        self._max_paraformer_errors = 3
        self._last_paraformer_check = None
        
        # 🎯 修复：捕获主事件循环用于线程安全的异步调用
        try:
            self.main_loop = asyncio.get_running_loop()
            logger.debug("✅ 成功获取主事件循环引用")
        except RuntimeError:
            logger.warning("⚠️ RealtimeManager初始化时没有运行中的事件循环")
            self.main_loop = None
        
        # Paraformer识别器实例
        self._paraformer_recognition = None
        
        # 注册内置事件处理器
        self._register_builtin_handlers()
    
    async def initialize_audio_splitter(self) -> bool:
        """初始化音频分发器 - 增强版错误处理"""
        try:
            if settings.enable_dual_asr:
                from .audio_stream_splitter import AudioStreamSplitter
                self.audio_splitter = AudioStreamSplitter()
                success = await self.audio_splitter.initialize(self)
                if success:
                    self.audio_splitter_initialized = True
                    logger.info("✅ 音频分发器初始化成功")
                else:
                    # 🎯 错误处理：初始化失败时的降级处理
                    logger.warning("⚠️ 音频分发器初始化失败，自动降级到单轨模式")
                    self.audio_splitter = None
                    self.audio_splitter_initialized = False
                return True  # 即使分发器失败，也要继续运行（单轨模式）
            else:
                logger.info("📝 双轨ASR已禁用，使用单轨模式")
                return True
        except Exception as e:
            logger.error(f"初始化音频分发器失败: {e}")
            # 🎯 错误处理：异常时降级到单轨模式
            logger.warning("⚠️ 初始化异常，自动降级到单轨模式")
            self.audio_splitter = None
            self.audio_splitter_initialized = False
            return True  # 继续运行单轨模式
    
    def _register_builtin_handlers(self) -> None:
        """注册内置事件处理器"""
        # 🎯 核心修复：注册语音检测事件处理器（barge-in关键功能）
        self.register_event_handler(
            "input_audio_buffer.speech_started",
            self._handle_speech_started
        )
        
        self.register_event_handler(
            "input_audio_buffer.speech_stopped",
            self._handle_speech_stopped
        )
        
        # 注册AI回复相关事件处理器
        self.register_event_handler(
            "response.audio_transcript.delta",
            self._handle_response_audio_transcript_delta
        )
        
        self.register_event_handler(
            "response.audio_transcript.done", 
            self._handle_response_audio_transcript_done
        )
        
        self.register_event_handler(
            "response.audio.delta",
            self._handle_response_audio_delta
        )
        
        self.register_event_handler(
            "response.audio.done",
            self._handle_response_audio_done
        )
        
        self.register_event_handler(
            "response.output_item.done",
            self._handle_response_output_item_done
        )
        
        self.register_event_handler(
            "response.content_part.done", 
            self._handle_response_content_part_done
        )
        
        # 🎯 重要修复：注册response.done事件处理器作为AI回复的唯一来源
        self.register_event_handler(
            "response.done",
            self._handle_response_done
        )
        
        # 注册用户输入音频转录事件处理器
        self.register_event_handler(
            "conversation.item.input_audio_transcription.delta",
            self._handle_input_audio_transcription_delta
        )
        
        self.register_event_handler(
            "conversation.item.input_audio_transcription.completed",
            self._handle_input_audio_transcription_completed
        )
        
        logger.debug("Registered builtin event handlers including speech detection for barge-in functionality")
    
    async def connect(self) -> bool:
        """
        连接到阿里千问qwen-omni-turbo-realtime API
        
        Returns:
            bool: 连接是否成功
        """
        if self.is_connected:
            logger.warning("Already connected to 阿里千问qwen-omni-turbo-realtime API")
            return True
        
        try:
            self.connection_attempts += 1
            
            # 按照千问官方文档要求构建URL和认证
            # URL格式：wss://dashscope.aliyuncs.com/api-ws/v1/realtime?model=qwen-omni-turbo-realtime
            from urllib.parse import urlencode
            
            # 只在URL中包含model参数（按照官方文档）
            params = {
                "model": self.model
            }
            
            url = f"{self.ws_endpoint}?{urlencode(params)}"
            
            # 使用标准的Authorization Bearer头进行认证（按照官方文档）
            headers = {
                "Authorization": f"Bearer {self.api_key}"
            }
            
            logger.info(f"Connecting to 阿里千问qwen-omni-turbo-realtime API (attempt {self.connection_attempts})")
            logger.debug(f"Connection URL: {url}")  # 🎯 修复：显示完整URL包含参数
            logger.debug(f"Model: {self.model}")
            logger.debug(f"Using Authorization Bearer token (length: {len(self.api_key) if self.api_key else 0})")
            
            # 建立连接 - 优化连接参数以提高稳定性
            self.websocket = await asyncio.wait_for(
                websockets.connect(
                    url,
                    extra_headers=headers,
                    ping_interval=60,      # 🎯 增加到60秒，进一步减少网络负载
                    ping_timeout=30,       # 🎯 增加到30秒，提高超时容忍度
                    close_timeout=15,      # 🎯 增加到15秒，更稳定的关闭
                    max_size=100 * 1024 * 1024,  # 保持100MB，支持大音频流
                    read_limit=4 * 1024 * 1024,  # 🎯 增加到4MB读缓冲区
                    write_limit=4 * 1024 * 1024, # 🎯 增加到4MB写缓冲区
                    compression=None       # 禁用压缩以减少延迟
                ),
                timeout=30  # 🎯 保持30秒连接超时
            )
            
            self.is_connected = True
            self.connection_attempts = 0  # 成功连接后重置计数
            self.ping_failures = 0
            
            # 启动任务
            await self._start_background_tasks()
            
            logger.info("Successfully connected to 阿里千问qwen-omni-turbo-realtime API")
            
            # 触发连接成功事件
            await self._dispatch_event("connection.established", {
                "timestamp": datetime.utcnow().isoformat()
            })
            
            return True
            
        except asyncio.TimeoutError:
            logger.error("Connection timeout to 阿里千问qwen-omni-turbo-realtime API")
            self.last_error = TimeoutError("Connection timeout")
            await self._handle_connection_failure()
            return False
        except Exception as e:
            logger.error(f"Failed to connect to 阿里千问qwen-omni-turbo-realtime API: {e}")
            self.last_error = e
            await self._handle_connection_failure()
            return False
    
    async def _start_background_tasks(self):
        """启动后台任务"""
        # 启动消息处理任务
        self.message_task = asyncio.create_task(self._handle_incoming_events())
        
        # 启动心跳监控任务
        self.heartbeat_task = asyncio.create_task(self._heartbeat_monitor())
    
    async def _stop_background_tasks(self):
        """停止后台任务"""
        tasks = [self.message_task, self.heartbeat_task, self.reconnect_task]
        
        for task in tasks:
            if task and not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
        
        self.message_task = None
        self.heartbeat_task = None
        self.reconnect_task = None
    
    async def disconnect(self) -> None:
        """
        关闭WebSocket连接并清理资源
        """
        try:
            logger.info("Disconnecting from 阿里千问qwen-omni-turbo-realtime API")
            
            # 禁用自动重连
            self.reconnect_enabled = False
            
            # 停止后台任务
            await self._stop_background_tasks()
            
            # 关闭WebSocket连接
            if self.websocket:
                await self.websocket.close()
                self.websocket = None
            
            # 重置状态
            self.is_connected = False
            self.is_session_active = False
            self.session_id = None
            self.ping_failures = 0
            
            # 触发断开连接事件
            await self._dispatch_event("connection.closed", {
                "timestamp": datetime.utcnow().isoformat()
            })
            
            logger.info("Disconnected from 阿里千问qwen-omni-turbo-realtime API")
            
        except Exception as e:
            logger.error(f"Error during disconnect: {e}")
    
    async def send_event(self, event: Dict[str, Any]) -> bool:
        """
        发送事件到阿里千问qwen-omni-turbo-realtime API
        
        Args:
            event: 要发送的事件数据
            
        Returns:
            bool: 发送是否成功
        """
        if not self.is_connected or not self.websocket:
            logger.error("Cannot send event: not connected")
            # 尝试自动重连
            if self.reconnect_enabled:
                asyncio.create_task(self._attempt_reconnect())
            return False
        
        try:
            message = json.dumps(event)
            await self.websocket.send(message)
            
            logger.debug(f"Sent event: {event.get('type', 'unknown')}")
            return True
            
        except (websockets.exceptions.ConnectionClosed, 
                websockets.exceptions.WebSocketException) as e:
            logger.warning(f"WebSocket connection lost while sending event: {e}")
            self.is_connected = False
            self.is_session_active = False
            if self.reconnect_enabled:
                asyncio.create_task(self._attempt_reconnect())
            return False
        except Exception as e:
            logger.error(f"Error sending event: {e}")
            return False
    
    def map_voice_to_qwen(self, voice_name: str) -> str:
        """
        将音色名称映射到千问支持的音色
        
        Args:
            voice_name: 原始音色名称
            
        Returns:
            str: 千问API支持的音色名称
        """
        voice_mapping = get_qwen_voice_mapping()
        mapped_voice = voice_mapping.get(voice_name, "Ethan")  # 默认使用Ethan
        
        if voice_name != mapped_voice:
            logger.info(f"Voice mapping: {voice_name} -> {mapped_voice}")
        
        return mapped_voice
    
    async def start_session(self, session_config: Dict[str, Any]) -> bool:
        """
        启动Realtime会话并配置参数（支持音色映射）
        
        Args:
            session_config: 会话配置参数
            
        Returns:
            bool: 会话启动是否成功
        """
        if not self.is_connected:
            logger.error("Cannot start session: not connected")
            return False
        
        try:
            # 处理音色映射
            mapped_config = session_config.copy()
            if "voice" in mapped_config:
                original_voice = mapped_config["voice"]
                mapped_voice = self.map_voice_to_qwen(original_voice)
                mapped_config["voice"] = mapped_voice
            
            # 确保千问必需的配置项
            qwen_defaults = {
                "modalities": ["text", "audio"],
                "input_audio_format": "pcm16",
                "output_audio_format": "pcm16",
                "input_audio_transcription": {
                    "model": "gummy-realtime-v1"
                }
                # 🎯 修复：移除硬编码的turn_detection，将从VAD管理器获取
            }
            
            # 合并默认配置与用户配置
            for key, value in qwen_defaults.items():
                if key not in mapped_config:
                    mapped_config[key] = value
            
            # 发送会话更新事件
            session_update = {
                "type": "session.update",
                "session": mapped_config
            }
            
            success = await self.send_event(session_update)
            if success:
                self.current_session_config = mapped_config
                self.is_session_active = True
                logger.info("Realtime session started successfully with Qwen defaults")
                logger.info(f"Session config: voice={mapped_config.get('voice', 'default')}, "
                          f"modalities={mapped_config.get('modalities', [])}")
                
                # 🎯 修复：Session激活后，尝试应用存储的VAD配置
                try:
                    from core.vad_manager import get_vad_manager
                    vad_manager = get_vad_manager()
                    if vad_manager:
                        await vad_manager.apply_stored_config_if_ready()
                except Exception as e:
                    logger.warning(f"Failed to apply stored VAD config after session start: {e}")
                
                # 触发会话启动事件
                await self._dispatch_event("session.started", {
                    "config": mapped_config,
                    "timestamp": datetime.utcnow().isoformat()
                })
            
            return success
            
        except Exception as e:
            logger.error(f"Error starting session: {e}")
            return False
    
    async def optimize_for_interruption(self) -> bool:
        """
        为主动打断场景优化VAD配置
        
        Returns:
            bool: 配置是否成功
        """
        try:
            # 🎯 修复：只优化打断相关参数，保留用户的silence_duration_ms配置
            # 获取当前配置
            current_turn_detection = self.current_session_config.get("turn_detection", {}) if self.current_session_config else {}
            
            # 🎯 使用VAD管理器配置而不是硬编码
            from core.vad_manager import get_vad_manager
            vad_manager = get_vad_manager()
            if vad_manager:
                # 使用当前VAD配置，启用打断功能
                vad_config = vad_manager.get_config()
                qwen_config = vad_config.to_qwen_config()
                qwen_config["interrupt_response"] = True  # 确保启用打断
                interruption_optimized_config = {
                    "turn_detection": qwen_config
                }
            else:
                # 如果VAD管理器不可用，使用保守配置
                interruption_optimized_config = {
                                         "turn_detection": {
                         "type": "server_vad",
                         "threshold": current_turn_detection.get("threshold", 0.5),
                         "prefix_padding_ms": current_turn_detection.get("prefix_padding_ms", 200),
                         "silence_duration_ms": current_turn_detection.get("silence_duration_ms", 800),
                         "create_response": True,
                         "interrupt_response": True
                     }
                 }
            
            success = await self.update_session_config(interruption_optimized_config)
            if success:
                logger.info(f"VAD configuration optimized for interruption scenarios (silence_duration: {interruption_optimized_config['turn_detection']['silence_duration_ms']}ms)")
            else:
                logger.error("Failed to optimize VAD configuration")
                
            return success
            
        except Exception as e:
            logger.error(f"Error optimizing VAD for interruption: {e}")
            return False
    
    async def pause_audio_input(self) -> bool:
        """
        暂停音频输入处理 - 通过插入空音频占位（新方案）

        Returns:
            bool: 暂停是否成功
        """
        try:
            # 🎯 新方案：插入5秒空音频来占位，阻止用户音频被处理
            success = await self.inject_silence_audio(duration_seconds=5.0)
            if success:
                logger.info("🛡️ Audio input paused by injecting 5s silence audio")

            return success

        except Exception as e:
            logger.error(f"Error pausing audio input: {e}")
            return False
    
    async def resume_audio_input(self) -> bool:
        """
        恢复音频输入处理 - 清空音频缓冲区（新方案）

        Returns:
            bool: 恢复是否成功
        """
        try:
            # 🎯 新方案：清空音频缓冲区，准备接收新的用户音频
            success = await self.clear_audio_buffer()
            if success:
                logger.info("✅ Audio input resumed by clearing audio buffer")

            return success

        except Exception as e:
            logger.error(f"Error resuming audio input: {e}")
            return False

    async def inject_silence_audio(self, duration_seconds: float = 5.0) -> bool:
        """
        向音频流注入指定时长的空音频，用于阻止用户音频被处理

        Args:
            duration_seconds: 空音频时长（秒）

        Returns:
            bool: 是否成功注入
        """
        try:
            if not self.is_connected or not self.is_session_active:
                logger.warning("Cannot inject silence: not connected or session not active")
                return False

            # 生成空音频数据 (PCM16, 24kHz, 单声道)
            sample_rate = 24000
            samples_per_second = sample_rate
            total_samples = int(duration_seconds * samples_per_second)

            # 创建空音频数据 (16位PCM，值为0表示静音)
            silence_audio = b'\x00\x00' * total_samples  # 每个样本2字节，值为0

            # 分块发送空音频 (避免单次发送过大数据)
            chunk_size = 4800  # 0.1秒的音频数据 (24000 * 0.1 * 2字节)
            total_chunks = len(silence_audio) // chunk_size

            logger.info(f"🔇 Injecting {duration_seconds}s silence audio ({total_chunks} chunks)")

            for i in range(0, len(silence_audio), chunk_size):
                chunk = silence_audio[i:i + chunk_size]
                success = await self._send_audio_to_qwen_direct(chunk)

                if not success:
                    logger.error(f"Failed to send silence chunk {i // chunk_size + 1}")
                    return False

                # 小延迟避免过载
                await asyncio.sleep(0.01)

            logger.info(f"✅ Successfully injected {duration_seconds}s silence audio")
            return True

        except Exception as e:
            logger.error(f"Error injecting silence audio: {e}")
            return False

    async def clear_audio_buffer(self) -> bool:
        """
        清空音频缓冲区，准备接收新的用户音频

        Returns:
            bool: 是否成功清空
        """
        try:
            if not self.is_connected or not self.is_session_active:
                logger.warning("Cannot clear audio buffer: not connected or session not active")
                return False

            # 发送清空音频缓冲区的命令
            clear_event = {
                "type": "input_audio_buffer.clear"
            }

            success = await self.send_event(clear_event)

            if success:
                logger.info("🗑️ Audio buffer cleared successfully")
            else:
                logger.error("Failed to clear audio buffer")

            return success

        except Exception as e:
            logger.error(f"Error clearing audio buffer: {e}")
            return False

    async def update_session_config(self, updates: Dict[str, Any]) -> bool:
        """
        动态更新Realtime会话配置（支持音色映射）
        
        Args:
            updates: 要更新的配置项（如instructions, voice等）
            
        Returns:
            bool: 更新是否成功
        """
        if not self.is_connected or not self.is_session_active:
            logger.error("Cannot update session: not connected or session not active")
            return False
            
        try:
            # 处理音色映射
            mapped_updates = updates.copy()
            if "voice" in mapped_updates:
                original_voice = mapped_updates["voice"]
                mapped_voice = self.map_voice_to_qwen(original_voice)
                mapped_updates["voice"] = mapped_voice
            
            # 合并当前配置与更新项
            if self.current_session_config:
                updated_config = {**self.current_session_config, **mapped_updates}
            else:
                updated_config = mapped_updates
            
            # 发送会话更新事件
            session_update = {
                "type": "session.update", 
                "session": updated_config
            }
            
            success = await self.send_event(session_update)
            if success:
                # 更新本地配置缓存
                self.current_session_config = updated_config
                logger.info(f"Session config updated successfully: {list(mapped_updates.keys())}")
                
                # 触发配置更新事件
                await self._dispatch_event("session.config.updated", {
                    "updates": mapped_updates,
                    "full_config": updated_config,
                    "timestamp": datetime.utcnow().isoformat()
                })
            else:
                logger.error("Failed to send session update to API")
                
            return success
            
        except Exception as e:
            logger.error(f"Error updating session config: {e}")
            return False
    
    def get_current_instructions(self) -> Optional[str]:
        """获取当前会话的指令（AI身份）"""
        if self.current_session_config and "instructions" in self.current_session_config:
            return self.current_session_config["instructions"]
        return None
    
    def register_event_handler(self, event_type: str, handler: Callable) -> None:
        """
        注册事件处理器
        
        Args:
            event_type: 事件类型（如 "response.audio.delta"）
            handler: 处理函数
        """
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = []
        
        self.event_handlers[event_type].append(handler)
        logger.debug(f"Registered handler for event: {event_type}")
    
    def unregister_event_handler(self, event_type: str, handler: Callable) -> None:
        """
        注销事件处理器
        
        Args:
            event_type: 事件类型
            handler: 处理函数
        """
        if event_type in self.event_handlers:
            try:
                self.event_handlers[event_type].remove(handler)
                logger.debug(f"Unregistered handler for event: {event_type}")
            except ValueError:
                logger.warning(f"Handler not found for event: {event_type}")
    
    async def _handle_incoming_events(self) -> None:
        """
        处理来自阿里千问qwen-omni-turbo-realtime API的WebSocket消息 - 增强错误处理和重连机制
        """
        try:
            async for message in self.websocket:
                try:
                    # 处理不同类型的消息
                    if isinstance(message, str):
                        data = json.loads(message)
                        event_type = data.get("type", "unknown")
                        
                        logger.debug(f"Received event: {event_type}")
                        
                        # 处理特殊的系统事件
                        await self._handle_system_events(data)
                        
                        # 分发到注册的处理器
                        await self._dispatch_event(event_type, data)
                    
                    elif isinstance(message, bytes):
                        # 处理二进制消息（可能是音频数据）
                        await self._handle_binary_message(message)
                    
                    # 更新最后活动时间
                    self.last_pong_time = datetime.utcnow()
                    self.ping_failures = 0
                        
                except json.JSONDecodeError:
                    logger.error(f"Invalid JSON message: {message}")
                except Exception as e:
                    logger.error(f"Error processing message: {e}")
                    
        except websockets.exceptions.ConnectionClosed as e:
            logger.warning(f"WebSocket connection closed: {e}")
            self.is_connected = False
            if self.reconnect_enabled:
                await self._attempt_reconnect()
        except websockets.exceptions.WebSocketException as e:
            logger.error(f"WebSocket exception: {e}")
            self.is_connected = False
            if self.reconnect_enabled:
                await self._attempt_reconnect()
        except Exception as e:
            logger.error(f"Unexpected error in message handling: {e}")
            self.is_connected = False
            if self.reconnect_enabled:
                await self._attempt_reconnect()
    
    async def _handle_binary_message(self, message: bytes) -> None:
        """
        处理二进制消息
        
        Args:
            message: 二进制消息数据
        """
        try:
            # 分发二进制数据事件
            await self._dispatch_event("binary.data", {"data": message})
        except Exception as e:
            logger.error(f"Error handling binary message: {e}")
    
    async def _heartbeat_monitor(self) -> None:
        """
        心跳监控任务 - 进一步优化稳定性，减少误触发重连
        """
        while self.is_connected:
            try:
                await asyncio.sleep(45)  # 🎯 增加到45秒检查一次，进一步减少频繁检查
                
                if not self.is_connected:
                    break
                
                # 检查ping/pong状态
                current_time = datetime.utcnow()
                
                if self.last_ping_time and self.last_pong_time:
                    # 🎯 优化超时检测：增加到180秒（3分钟），大幅提高容忍度
                    if (current_time - self.last_pong_time).total_seconds() > 180:
                        self.ping_failures += 1
                        logger.warning(f"Ping failure detected, count: {self.ping_failures}")
                        
                        # 🎯 容忍度增加到8次，避免网络波动导致的误触发
                        if self.ping_failures >= 8:
                            logger.error("Too many ping failures, triggering reconnect")
                            self.is_connected = False
                            if self.reconnect_enabled:
                                await self._attempt_reconnect()
                            break
                
                # 发送自定义心跳
                await self._send_heartbeat()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in heartbeat monitor: {e}")
    
    async def _send_heartbeat(self) -> None:
        """发送心跳事件 - 减少日志噪音，优化监控"""
        try:
            current_time = datetime.utcnow()
            self.last_ping_time = current_time
            
            # 🎯 详细连接质量监控 - 调整阈值减少警告
            if self.last_pong_time:
                time_since_last_pong = (current_time - self.last_pong_time).total_seconds()
                
                # 根据响应时间评估连接质量 - 放宽阈值
                if time_since_last_pong > 150:  # 从100秒调整到150秒
                    connection_quality = "poor"
                    logger.warning(f"⚠️ 连接质量差: {time_since_last_pong:.1f}秒未收到响应")
                elif time_since_last_pong > 90:  # 从60秒调整到90秒
                    connection_quality = "degraded"
                    # 减少日志频率 - 只在质量下降时记录
                    if not hasattr(self, '_last_degraded_log') or current_time.timestamp() - self._last_degraded_log > 60:
                        logger.info(f"📊 连接质量下降: {time_since_last_pong:.1f}秒未收到响应")
                        self._last_degraded_log = current_time.timestamp()
                else:
                    connection_quality = "good"
                    # 只在调试模式下记录良好状态
                    logger.debug(f"✅ 连接质量良好: {time_since_last_pong:.1f}秒前收到响应")
                
                # 只在质量变化时触发事件，减少事件噪音
                if not hasattr(self, '_last_connection_quality') or self._last_connection_quality != connection_quality:
                    await self._dispatch_event("connection.quality.update", {
                        "quality": connection_quality,
                        "time_since_last_pong": time_since_last_pong,
                        "ping_failures": self.ping_failures,
                        "timestamp": current_time.isoformat()
                    })
                    self._last_connection_quality = connection_quality
            
            # 🎯 减少心跳日志噪音 - 只在失败次数增加时记录
            if self.ping_failures > 0:
                logger.debug(f"💓 心跳发送: 连接活跃, 失败次数: {self.ping_failures}")
            
        except Exception as e:
            logger.error(f"💥 心跳发送错误: {e}")
            self.ping_failures += 1
    
    async def _handle_system_events(self, data: Dict[str, Any]) -> None:
        """
        处理系统级事件（扩展支持千问特有事件）
        
        Args:
            data: 事件数据
        """
        event_type = data.get("type")
        
        if event_type == "session.created":
            self.session_id = data.get("session", {}).get("id")
            session_data = data.get("session", {})
            
            # 记录VAD配置信息
            turn_detection = session_data.get("turn_detection", {})
            if turn_detection.get("type") == "server_vad":
                logger.info(f"Session created with server VAD: "
                          f"threshold={turn_detection.get('threshold', 'default')}, "
                          f"silence_duration={turn_detection.get('silence_duration_ms', 'default')}ms")
            else:
                logger.info(f"Session created with turn detection: {turn_detection.get('type', 'unknown')}")
                
            logger.info(f"Session created: {self.session_id}")
            
        elif event_type == "error":
            error_info = data.get("error", {})
            logger.error(f"阿里千问qwen-omni-turbo-realtime API error: {error_info}")
            
            # 使用增强的错误处理
            await self._handle_api_error(error_info)

        # 千问特有VAD事件处理（增强版）
        elif event_type == "input_audio_buffer.speech_started":
            logger.info("🎤 VAD detected speech started")
            # 记录语音开始时间用于统计
            speech_start_data = {
                **data,
                "local_timestamp": datetime.utcnow().isoformat()
            }
            # 🎯 **修复：** 发布明确的 *USER* 事件
            await self._dispatch_event("user.speech.started", speech_start_data)
            
        elif event_type == "input_audio_buffer.speech_stopped":
            logger.info("🔇 VAD detected speech stopped")
            # 记录语音结束时间用于统计
            speech_stop_data = {
                **data,
                "local_timestamp": datetime.utcnow().isoformat()
            }
            # 🎯 **修复：** 发布明确的 *USER* 事件
            await self._dispatch_event("user.speech.stopped", speech_stop_data)
            
            # 如果启用了自动响应，VAD会自动触发response.create
            turn_detection = self.current_session_config.get("turn_detection", {}) if self.current_session_config else {}
            if turn_detection.get("create_response", False):
                logger.info("VAD will auto-create response due to speech end")
            
        elif event_type == "input_audio_buffer.committed":
            logger.info("✅ Audio buffer committed for processing")
            await self._dispatch_event("audio.committed", data)
            
        elif event_type == "response.created":
            response_id = data.get("response", {}).get("id")
            logger.info(f"📝 Response created: {response_id}")
            self.is_response_active = True  # 确认响应激活
            
        elif event_type == "response.started":
            logger.info("🔊 AI speech started.")
            # 🎯 **修复：** 发布明确的 *AI* 事件
            ai_speech_data = {
                **data,
                "local_timestamp": datetime.utcnow().isoformat()
            }
            await self._dispatch_event("ai.speech.started", ai_speech_data)
            
        elif event_type == "response.done":
            response = data.get("response", {})
            response_id = response.get("id", "unknown")
            status = response.get("status", "unknown")
            logger.info(f"✅ Response completed: {response_id} (status: {status})")
            self.is_response_active = False  # 清除响应状态
            # 🎯 **修复：** 发布明确的 *AI* 事件
            ai_speech_end_data = {
                **data,
                "local_timestamp": datetime.utcnow().isoformat()
            }
            await self._dispatch_event("ai.speech.ended", ai_speech_end_data)
            
        elif event_type == "conversation.item.created":
            item = data.get("item", {})
            item_id = item.get("id", "unknown")
            item_type = item.get("type", "unknown")
            logger.info(f"💬 Conversation item created: {item_id} (type: {item_type})")
            
        elif event_type == "conversation.item.input_audio_transcription.completed":
            transcript = data.get("transcript", "")
            item_id = data.get("item_id", "unknown")
            logger.info(f"📝 Input transcription completed for {item_id}: '{transcript}'")
            
        elif event_type == "conversation.item.input_audio_transcription.delta":
            # 直接调用专门的delta处理器
            await self._handle_input_audio_transcription_delta(event_type, data)
            
        elif event_type == "response.audio_transcript.delta":
            # 处理音频转录增量
            delta = data.get("delta", "")
            logger.debug(f"🎵 Audio transcript delta: '{delta}'")
            
        elif event_type == "response.audio_transcript.done":
            transcript = data.get("transcript", "")
            logger.info(f"🎵 Audio transcript completed: '{transcript}'")
    
    async def _dispatch_event(self, event_type: str, data: Dict[str, Any]) -> None:
        """
        分发事件到注册的处理器
        
        Args:
            event_type: 事件类型
            data: 事件数据
        """
        handlers = self.event_handlers.get(event_type, [])
        
        # ✅ 增强：对转录和音频事件添加详细日志
        is_critical_event = event_type in [
            "conversation.item.input_audio_transcription.delta", 
            "conversation.item.input_audio_transcription.completed",
            "response.audio.delta", 
            "response.audio.done",
            "response.audio_transcript.delta",
            "response.audio_transcript.done"
        ]
        
        if handlers:
            logger.debug(f"📤 Dispatching '{event_type}' to {len(handlers)} handlers")
            for handler in handlers:
                try:
                    await handler(event_type, data)
                except Exception as e:
                    logger.error(f"Error in event handler for '{event_type}': {e}")
        elif is_critical_event:
            logger.warning(f"⚠️ No handlers for critical event: {event_type}")
        else:
            logger.debug(f"📭 No handlers for event: {event_type}")
            
        # 🎯 **新增：** 发布特定的语音事件到消息总线
        try:
            from .message_bus import message_bus, TopicNames, create_message
            from .event_types import EventType, MessageType
            
            if event_type == "user.speech.started":
                logger.info("🎤 Publishing USER_SPEECH_STARTED to Orchestrator.")
                speech_event_message = create_message(
                    event_type=EventType.SPEECH_STARTED,
                    message_type=MessageType.USER_INPUT,
                    data={
                        "speech_started": True, 
                        "timestamp": datetime.utcnow().timestamp(),
                        "session_id": self.current_session_id
                    },
                    source_agent="realtime_manager"
                )
                await message_bus.publish(TopicNames.USER_SPEECH_STARTED, speech_event_message)
                
            elif event_type == "user.speech.stopped":
                logger.info("🔇 Publishing USER_SPEECH_ENDED to Orchestrator.")
                speech_event_message = create_message(
                    event_type=EventType.SPEECH_STOPPED,
                    message_type=MessageType.USER_INPUT,
                    data={
                        "speech_started": False, 
                        "timestamp": datetime.utcnow().timestamp(),
                        "session_id": self.current_session_id
                    },
                    source_agent="realtime_manager"
                )
                await message_bus.publish(TopicNames.USER_SPEECH_ENDED, speech_event_message)
                
            elif event_type == "ai.speech.started":
                logger.info("🔊 Publishing AI_SPEECH_STARTED for state management.")
                ai_speech_message = create_message(
                    event_type=EventType.SPEECH_STARTED,
                    message_type=MessageType.SPEAKER_AUDIO,
                    data={
                        "speech_started": True, 
                        "timestamp": datetime.utcnow().timestamp(),
                        "session_id": self.current_session_id
                    },
                    source_agent="realtime_manager"
                )
                await message_bus.publish(TopicNames.AI_SPEECH_STARTED, ai_speech_message)
                
            elif event_type == "ai.speech.ended":
                logger.info("🔇 Publishing AI_SPEECH_ENDED for state management.")
                ai_speech_message = create_message(
                    event_type=EventType.SPEECH_STOPPED,
                    message_type=MessageType.SPEAKER_AUDIO,
                    data={
                        "speech_started": False, 
                        "timestamp": datetime.utcnow().timestamp(),
                        "session_id": self.current_session_id
                    },
                    source_agent="realtime_manager"
                )
                await message_bus.publish(TopicNames.AI_SPEECH_ENDED, ai_speech_message)
                
        except Exception as e:
            logger.error(f"Error publishing speech event to message bus: {e}")
    
    async def _handle_connection_failure(self) -> None:
        """处理连接失败"""
        self.is_connected = False
        
        if self.reconnect_enabled and self.connection_attempts < self.max_connection_attempts:
            await self._attempt_reconnect()
        else:
            logger.error("Max connection attempts reached, stopping reconnection")
    
    async def _attempt_reconnect(self) -> None:
        """尝试重新连接"""
        if self.reconnect_task and not self.reconnect_task.done():
            return  # 重连任务已在进行中
        
        self.reconnect_task = asyncio.create_task(self._reconnect_loop())
    
    async def _reconnect_loop(self) -> None:
        """增强重连循环 - 改进稳定性和错误处理"""
        try:
            reconnect_attempt = 0
            while (self.reconnect_enabled and 
                   not self.is_connected and 
                   reconnect_attempt < self.max_connection_attempts):
                
                reconnect_attempt += 1
                
                # 🎯 改进指数退避算法，增加随机抖动避免雷群效应
                import random
                base_delay = min(self.reconnect_delay * (2 ** (reconnect_attempt - 1)), self.max_reconnect_delay)
                jitter = random.uniform(0.1, 0.3) * base_delay  # 10-30%的随机抖动
                delay = base_delay + jitter
                
                logger.info(f"Attempting reconnection {reconnect_attempt}/{self.max_connection_attempts} in {delay:.1f} seconds...")
                await asyncio.sleep(delay)
                
                if not self.reconnect_enabled:
                    logger.info("Reconnection disabled, stopping reconnect loop")
                    break
                
                # 🎯 改进连接清理，添加优雅关闭
                if self.websocket:
                    try:
                        logger.debug("Closing old websocket connection...")
                        await asyncio.wait_for(self.websocket.close(), timeout=5.0)
                    except asyncio.TimeoutError:
                        logger.warning("Websocket close timeout, forcing cleanup")
                    except Exception as e:
                        logger.debug(f"Error closing old websocket: {e}")
                    finally:
                        self.websocket = None
                
                # 🎯 在重连前重置相关状态
                self.ping_failures = 0
                self.last_ping_time = None
                self.last_pong_time = None
                
                # 尝试重新连接
                logger.info(f"Initiating reconnection attempt {reconnect_attempt}...")
                success = await self.connect()
                
                if success:
                    logger.info(f"✅ Reconnection successful after {reconnect_attempt} attempts")
                    
                    # 🎯 改进会话恢复，添加延迟和错误处理
                    if self.current_session_config:
                        try:
                            await asyncio.sleep(1.0)  # 给连接一点稳定时间
                            session_restored = await self.start_session(self.current_session_config)
                            if session_restored:
                                logger.info("✅ Session configuration restored successfully")
                            else:
                                logger.warning("⚠️ Session restoration failed, but connection established")
                        except Exception as session_error:
                            logger.error(f"❌ Session restoration error: {session_error}")
                    
                    break
                else:
                    logger.warning(f"❌ Reconnection attempt {reconnect_attempt} failed")
                    
                    # 🎯 添加失败后的额外等待，避免过于频繁的重试
                    if reconnect_attempt < self.max_connection_attempts:
                        await asyncio.sleep(1.0)
            
            if not self.is_connected and reconnect_attempt >= self.max_connection_attempts:
                logger.error(f"❌ All {self.max_connection_attempts} reconnection attempts failed")
                await self._dispatch_event("connection.failed", {
                    "attempts": reconnect_attempt,
                    "last_error": str(self.last_error) if self.last_error else None,
                    "final_status": "max_attempts_exceeded"
                })
        
        except asyncio.CancelledError:
            logger.info("🛑 Reconnection cancelled")
        except Exception as e:
            logger.error(f"💥 Error in reconnection loop: {e}")
            await self._dispatch_event("connection.error", {
                "error": str(e),
                "context": "reconnect_loop"
            })
    
    async def reconnect_with_backoff(self, max_retries: int = 3) -> bool:
        """
        使用指数退避重连
        
        Args:
            max_retries: 最大重试次数
            
        Returns:
            bool: 重连是否成功
        """
        for attempt in range(max_retries):
            if self.is_connected:
                return True
            
            wait_time = 2 ** attempt
            logger.info(f"Reconnection attempt {attempt + 1}/{max_retries} in {wait_time}s")
            
            await asyncio.sleep(wait_time)
            
            if await self.connect():
                # 重新配置会话
                if self.current_session_config:
                    await self.start_session(self.current_session_config)
                return True
        
        logger.error(f"Failed to reconnect after {max_retries} attempts")
        return False
    
    def is_connection_healthy(self) -> bool:
        """
        检查连接健康状态
        
        Returns:
            bool: 连接是否健康
        """
        return (
            self.is_connected and 
            self.websocket is not None and 
            not self.websocket.closed and
            self.message_task is not None and 
            not self.message_task.done()
        )
    
    async def handle_connection_error(self, error: Exception) -> None:
        """
        处理连接错误
        
        Args:
            error: 错误对象
        """
        logger.error(f"Connection error: {error}")
        self.last_error = error
        self.is_connected = False
        
        # 触发错误事件
        await self._dispatch_event("connection.error", {
            "error": str(error),
            "timestamp": datetime.utcnow().isoformat()
        })
        
        # 尝试自动重连
        if settings.auto_reconnect:
            logger.info("Attempting automatic reconnection...")
            await self.reconnect_with_backoff()
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取连接状态信息 - 增强直接连接状态
        
        Returns:
            Dict: 状态信息
        """
        base_status = {
            "is_connected": self.is_connected,
            "is_session_active": self.is_session_active,
            "session_id": self.session_id,
            "model": self.model,
            "connection_attempts": self.connection_attempts,
            "is_healthy": self.is_connection_healthy(),
            "last_error": str(self.last_error) if self.last_error else None,
            "event_handlers_count": {k: len(v) for k, v in self.event_handlers.items()}
        }
        
        # 添加直接连接状态
        base_status["direct_audio"] = self.get_direct_connections_status()
        
        return base_status
    
    async def pause_audio_input(self) -> bool:
        """
        暂停音频输入以避免VAD干扰
        
        Returns:
            bool: 是否成功暂停
        """
        try:
            if not self.is_connected:
                logger.warning("Cannot pause audio input: not connected")
                return False
                
            # 清空音频输入缓冲区
            clear_event = {
                "type": "input_audio_buffer.clear"
            }
            
            await self.send_event(clear_event)
            logger.debug("Audio input paused (buffer cleared)")
            return True
            
        except Exception as e:
            logger.error(f"Error pausing audio input: {e}")
            return False
    
    async def resume_audio_input(self) -> bool:
        """
        恢复音频输入监听
        
        Returns:
            bool: 是否成功恢复
        """
        try:
            if not self.is_connected:
                logger.warning("Cannot resume audio input: not connected")
                return False
                
            # 阿里千问qwen-omni-turbo-realtime API会自动恢复音频输入监听
            # 只需要确保会话处于正确状态
            logger.debug("Audio input resumed (automatic)")
            return True
            
        except Exception as e:
            logger.error(f"Error resuming audio input: {e}")
            return False
    
    def validate_audio_format(self, audio_data: bytes, expected_format: str = "pcm16") -> bool:
        """
        验证音频格式是否符合千问API要求
        
        Args:
            audio_data: 音频数据
            expected_format: 期望的音频格式 (pcm16)
            
        Returns:
            bool: 格式是否有效
        """
        try:
            # 基本长度检查 - PCM16每样本2字节
            if len(audio_data) % 2 != 0:
                logger.warning("Audio data length not aligned to 16-bit samples")
                return False
                
            # 检查是否为空数据
            if len(audio_data) == 0:
                logger.warning("Empty audio data")
                return False
                
            logger.debug(f"Audio format validation passed: {len(audio_data)} bytes")
            return True
            
        except Exception as e:
            logger.error(f"Audio format validation error: {e}")
            return False
    
    def ensure_audio_compatibility(self, audio_data: bytes) -> bytes:
        """
        确保音频数据与千问API兼容
        
        Args:
            audio_data: 原始音频数据
            
        Returns:
            bytes: 兼容的音频数据
        """
        try:
            # 验证格式
            if not self.validate_audio_format(audio_data):
                logger.warning("Audio format validation failed, attempting to process anyway")
            
            # 千问API要求：16bit 24kHz PCM，单声道
            # 这里假设输入已经是正确格式，实际项目中可能需要音频转换
            
            return audio_data
            
        except Exception as e:
            logger.error(f"Audio compatibility processing error: {e}")
            return audio_data

    async def interrupt_audio_output(self) -> bool:
        """
        中断当前音频输出
        
        Returns:
            bool: 中断是否成功
        """
        try:
            if not self.is_connected:
                logger.warning("Cannot interrupt audio: not connected")
                return False
                
            self.audio_interrupted = True
            self.is_outputting_audio = False
            
            # 发送响应取消事件
            if self.current_audio_response_id:
                cancel_message = {
                    "type": "response.cancel",
                    "event_id": f"event_{int(datetime.utcnow().timestamp() * 1000)}",
                    "response_id": self.current_audio_response_id
                }
                
                await self.send_event(cancel_message)
                logger.info(f"🛑 Audio output interrupted for response: {self.current_audio_response_id}")
                
                # 🎯 新增：预先清除响应状态
                self.is_response_active = False
                
                # 通知前端停止音频播放
                from .message_bus import message_bus, TopicNames, create_message
                from .event_types import EventType, MessageType
                
                interrupt_message = create_message(
                    event_type=EventType.SPEECH_INTERRUPTION,
                    message_type=MessageType.SYSTEM_NOTIFICATION,
                    data={"reason": "speech_detected", "interrupted_response_id": self.current_audio_response_id},
                    source_agent="realtime_manager"
                )
                
                await message_bus.publish(TopicNames.SPEECH_INTERRUPTION, interrupt_message)
                
            # 清空音频缓冲区
            self.audio_output_buffer.clear()
            self.current_audio_response_id = None
            
            return True
            
        except Exception as e:
            logger.error(f"Error interrupting audio output: {e}")
            return False

    async def handle_speech_detection(self, speech_started: bool) -> None:
        """
        Handles speech detection events and publishes them to the message bus
        for the Orchestrator to process.
        
        Args:
            speech_started: True if speech started, False if speech stopped
        """
        try:
            from .message_bus import message_bus, TopicNames, create_message
            from .event_types import EventType, MessageType

            # Determine the correct event type based on the VAD signal
            event_type = EventType.SPEECH_STARTED if speech_started else EventType.SPEECH_STOPPED
            
            logger.info(f"🎤 Publishing {event_type.value} to Orchestrator.")

            # Create a message that the Orchestrator is expecting
            speech_event_message = create_message(
                event_type=event_type,
                message_type=MessageType.USER_INPUT, # This is now correctly handled
                data={
                    "speech_started": speech_started, 
                    "timestamp": datetime.utcnow().timestamp(),
                    "session_id": self.current_session_id
                },
                source_agent="realtime_manager"
            )
            
            # Publish to the SPEECH_EVENTS topic that the Orchestrator subscribes to
            await message_bus.publish(TopicNames.SPEECH_EVENTS, speech_event_message)
            
        except Exception as e:
            # Use exc_info=True for a full stack trace to better understand future errors
            logger.error(f"Error in handle_speech_detection: {e}", exc_info=True)

    async def force_speech_end(self):
        """
        🎯 强制结束当前语音检测，用于处理用户手动停止录音的情况
        通过发送1.2秒的静音音频来触发VAD自然检测语音结束
        """
        try:
            logger.info("🎯 Force ending speech detection by sending silence audio")

            # 如果当前有活跃的语音检测，发送静音音频触发VAD
            if self.is_connected and self.is_session_active:
                # 🎯 新方案：发送1.2秒的静音音频来触发VAD
                # 这比手动干预更自然，让VAD系统按正常流程工作
                silence_duration = 1.2  # 秒，大于VAD的0.9秒阈值
                sample_rate = 24000  # 24kHz
                samples_count = int(silence_duration * sample_rate)

                # 创建静音音频数据（PCM16格式，全零）
                silence_audio = bytes(samples_count * 2)  # 2 bytes per sample for 16-bit

                logger.info(f"🎯 Sending {silence_duration}s silence audio ({len(silence_audio)} bytes) to trigger VAD")

                # 发送静音音频到缓冲区
                success = await self.send_audio_buffer_append(silence_audio)

                if success:
                    logger.info("✅ Silence audio sent successfully - VAD should detect speech end naturally")
                else:
                    logger.error("❌ Failed to send silence audio")

                logger.info("✅ Force speech end completed")
            else:
                logger.warning("⚠️ Cannot force speech end - not connected or no active session")

        except Exception as e:
            logger.error(f"💥 Error in force_speech_end: {e}")

    async def send_audio_chunks(self, audio_data: bytes, chunk_size: int = 8192) -> bool:
        """
        将大音频数据分块发送
        
        Args:
            audio_data: 完整的音频数据
            chunk_size: 每个分块的大小
            
        Returns:
            bool: 发送是否成功
        """
        try:
            if not self.is_connected:
                logger.error("Cannot send audio chunks: not connected")
                return False
            
            # 确保音频格式兼容
            compatible_audio = self.ensure_audio_compatibility(audio_data)
            
            # 计算分块数量
            total_chunks = (len(compatible_audio) + chunk_size - 1) // chunk_size
            session_id = f"audio_{int(datetime.utcnow().timestamp() * 1000)}"
            
            logger.info(f"📦 Sending audio in {total_chunks} chunks (chunk_size: {chunk_size})")
            
            # 发送每个分块
            for chunk_index in range(total_chunks):
                start_pos = chunk_index * chunk_size
                end_pos = min(start_pos + chunk_size, len(compatible_audio))
                chunk_data = compatible_audio[start_pos:end_pos]
                
                # 发送分块
                success = await self.send_audio_chunk(
                    chunk_data, 
                    chunk_index, 
                    total_chunks, 
                    session_id
                )
                
                if not success:
                    logger.error(f"Failed to send audio chunk {chunk_index + 1}/{total_chunks}")
                    return False
                
                # 添加小延迟避免过载
                if chunk_index < total_chunks - 1:
                    await asyncio.sleep(0.01)  # 10ms delay
            
            logger.info(f"✅ Successfully sent all {total_chunks} audio chunks")
            return True
            
        except Exception as e:
            logger.error(f"Error sending audio chunks: {e}")
            return False

    async def send_audio_chunk(self, chunk_data: bytes, chunk_index: int, 
                              total_chunks: int, session_id: str) -> bool:
        """
        发送单个音频分块
        
        Args:
            chunk_data: 分块数据
            chunk_index: 分块索引
            total_chunks: 总分块数
            session_id: 会话ID
            
        Returns:
            bool: 发送是否成功
        """
        try:
            import base64
            
            # 将音频数据编码为base64
            chunk_b64 = base64.b64encode(chunk_data).decode()
            
            # 创建分块消息
            chunk_message = {
                "type": "input_audio_buffer.chunk",
                "event_id": f"event_{int(datetime.utcnow().timestamp() * 1000)}_{chunk_index}",
                "chunk_index": chunk_index,
                "total_chunks": total_chunks,
                "session_id": session_id,
                "audio": chunk_b64,
                "format": "pcm16",
                "sample_rate": 24000,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            # 发送消息
            await self._send_json_message(chunk_message)
            
            logger.debug(f"📤 Sent audio chunk {chunk_index + 1}/{total_chunks} " +
                        f"({len(chunk_data)} bytes)")
            
            return True
            
        except Exception as e:
            logger.error(f"Error sending audio chunk {chunk_index}: {e}")
            return False

    async def process_large_audio_response(self, audio_data: str, response_id: str) -> None:
        """
        处理大型音频响应，分块发送到前端
        
        Args:
            audio_data: base64编码的音频数据
            response_id: 响应ID
        """
        try:
            from .message_bus import message_bus, TopicNames, create_message
            from .event_types import EventType, MessageType
            
            # 解码音频数据以计算大小
            import base64
            decoded_size = len(base64.b64decode(audio_data))
            
            # 如果音频数据较小，直接发送
            if decoded_size < 32768:  # 32KB
                message_data = create_message(
                    event_type=EventType.AUDIO_OUTPUT,
                    message_type=MessageType.SPEAKER_AUDIO,
                    data={
                        "audio_data": audio_data,
                        "format": "pcm16",
                        "sample_rate": 24000,
                        "response_id": response_id,
                        "is_chunked": False
                    },
                    source_agent="realtime_manager"
                )
                
                await message_bus.publish(TopicNames.AUDIO_OUTPUT, message_data)
                return
            
            # 对于大音频数据，分块发送
            chunk_size = 16384  # 16KB per chunk (in base64)
            total_chunks = (len(audio_data) + chunk_size - 1) // chunk_size
            
            logger.info(f"📦 Processing large audio response in {total_chunks} chunks")
            
            for chunk_index in range(total_chunks):
                start_pos = chunk_index * chunk_size
                end_pos = min(start_pos + chunk_size, len(audio_data))
                chunk_audio = audio_data[start_pos:end_pos]
                
                message_data = create_message(
                    event_type=EventType.AUDIO_OUTPUT,
                    message_type=MessageType.SPEAKER_AUDIO,
                    data={
                        "audio_data": chunk_audio,
                        "format": "pcm16",
                        "sample_rate": 24000,
                        "response_id": response_id,
                        "is_chunked": True,
                        "chunk_index": chunk_index,
                        "total_chunks": total_chunks
                    },
                    source_agent="realtime_manager"
                )
                
                await message_bus.publish(TopicNames.AUDIO_OUTPUT, message_data)
                
                # 小延迟避免前端播放器过载
                if chunk_index < total_chunks - 1:
                    await asyncio.sleep(0.02)  # 20ms delay
            
            logger.info(f"✅ Finished processing large audio response")
            
        except Exception as e:
            logger.error(f"Error processing large audio response: {e}")

    async def send_audio_buffer_append(self, audio_data: bytes) -> bool:
        """
        向音频缓冲区追加音频数据并集成paraformer-realtime-v2实时转录
        
        Args:
            audio_data: PCM16格式的音频数据 (16bit, 24kHz, 单声道)
            
        Returns:
            bool: 发送是否成功
        """
        if not self.is_connected or not self.websocket:
            logger.debug("Cannot append audio buffer: not connected or websocket unavailable")
            return False

        if not self.is_session_active:
            logger.debug("Cannot append audio buffer: session not active")
            return False
            
        try:
            # 原有千问逻辑保持不变
            qwen_success = await self._send_audio_to_qwen_direct(audio_data)
            
            # 🎯 集成paraformer-realtime-v2实时转录
            # 检查是否需要尝试恢复Paraformer服务
            await self._check_paraformer_recovery()
            
            # 只有在不处于降级模式时才处理Paraformer转录
            if not self._paraformer_fallback:
                try:
                    # 初始化转录器（如果还没有初始化）
                    if not hasattr(self, '_paraformer_recognition') or self._paraformer_recognition is None:
                        # 导入必要的模块
                        import dashscope
                        from dashscope.audio.asr import Recognition, RecognitionCallback, RecognitionResult
                        import numpy as np
                        
                        # 设置API密钥
                        dashscope.api_key = self.api_key
                        
                        # 定义线程安全的回调类
                        class ParaformerTranscriptionCallback(RecognitionCallback):
                            def __init__(self, realtime_manager, main_loop):
                                super().__init__()
                                self.event_count = 0
                                self.realtime_manager = realtime_manager
                                self.main_loop = main_loop  # 🎯 关键：保存主事件循环引用
                                self.last_transcript = ""  # 用于增量计算
                            
                            def on_open(self) -> None:
                                logger.info("🎤 Paraformer-realtime-v2 ASR stream opened")
                            
                            def on_close(self) -> None:
                                logger.info("🎤 Paraformer-realtime-v2 ASR stream closed")
                            
                            def on_error(self, result: RecognitionResult) -> None:
                                logger.error(f"🚨 Paraformer ASR error: {result.get_message()}")
                                if self.realtime_manager:
                                    self.realtime_manager._handle_paraformer_error()
                            
                            def on_event(self, result: RecognitionResult) -> None:
                                try:
                                    self.event_count += 1
                                    
                                    # 🎯 核心功能：正确获取转录文本
                                    sentence_data = result.get_sentence()
                                    if not sentence_data or 'text' not in sentence_data:
                                        return
                                    
                                    full_text = sentence_data['text']
                                    
                                    # 只处理有变化的文本，避免重复
                                    if full_text == self.last_transcript:
                                        return
                                    
                                    # 计算增量部分
                                    delta = full_text[len(self.last_transcript):]
                                    self.last_transcript = full_text
                                    
                                    # 只发布有意义的增量
                                    if not delta.strip():
                                        return
                                    
                                    logger.info(f"🎵 Paraformer DELTA: '{delta}' (Full: '{full_text}')")
                                    
                                    # 🎯 关键修复：使用 run_coroutine_threadsafe 从回调线程安全调用异步函数
                                    if self.main_loop and self.main_loop.is_running():
                                        future = asyncio.run_coroutine_threadsafe(
                                            self.realtime_manager._publish_paraformer_delta(delta, full_text, result),
                                            self.main_loop
                                        )
                                        # 可选：等待结果，但不阻塞太久
                                        # future.result(timeout=1.0)
                                    else:
                                        logger.error("🚨 主事件循环未运行，无法发布Paraformer增量")
                                    
                                except Exception as e:
                                    logger.exception(f"🚨 Paraformer事件回调处理错误: {e}")
                        
                        # 初始化转录器，传入主事件循环
                        if not self.main_loop:
                            # 如果初始化时没有获取到，再次尝试
                            try:
                                self.main_loop = asyncio.get_running_loop()
                                logger.debug("✅ 在Paraformer初始化时成功获取事件循环")
                            except RuntimeError:
                                logger.error("🚨 无法获取事件循环，Paraformer可能无法正常工作")
                        
                        callback = ParaformerTranscriptionCallback(self, self.main_loop)
                        logger.info("🔧 正在初始化 paraformer-realtime-v2 转录器...")
                        
                        self._paraformer_recognition = Recognition(
                            model="paraformer-realtime-v2",
                            format="pcm",
                            sample_rate=16000,
                            language_hints=['zh', 'en'],
                            callback=callback
                        )
                        
                        logger.info("🔧 正在启动转录器...")
                        self._paraformer_recognition.start()
                        logger.info("✅ paraformer-realtime-v2 转录器已初始化并启动")
                    
                    # 音频格式转换：从24kHz降采样到16kHz
                    if self._paraformer_recognition is not None:
                        logger.debug("🔧 开始处理音频数据进行转录...")
                        try:
                            import numpy as np
                            # 将bytes转换为numpy数组
                            audio_array = np.frombuffer(audio_data, dtype=np.int16)
                            logger.debug(f"🔍 原始音频数组长度: {len(audio_array)}")
                            
                            # 降采样到16kHz
                            target_length = int(len(audio_array) * 16000 / 24000)
                            indices = np.linspace(0, len(audio_array) - 1, target_length).astype(int)
                            resampled_array = audio_array[indices]
                            transcription_audio = resampled_array.astype(np.int16).tobytes()
                            
                            logger.debug(f"🔍 转换后音频长度: {len(transcription_audio)} bytes")
                            
                            # 发送给转录器
                            self._paraformer_recognition.send_audio_frame(transcription_audio)
                            logger.debug(f"✅ 音频已发送给转录器: {len(audio_data)} bytes -> {len(transcription_audio)} bytes")
                            
                        except Exception as conversion_error:
                            logger.warning(f"⚠️ 音频格式转换失败，使用原始音频: {conversion_error}")
                            # 直接使用原始音频
                            self._paraformer_recognition.send_audio_frame(audio_data)
                            logger.debug(f"✅ 原始音频已发送给转录器: {len(audio_data)} bytes")
                    else:
                        logger.warning("⚠️ 转录器未初始化，跳过转录")
                        
                except Exception as transcription_error:
                    logger.error(f"🚨 转录处理失败，继续正常音频发送: {transcription_error}")
                    logger.info(f"🔍 转录错误详情: {type(transcription_error).__name__}: {str(transcription_error)}")
                    
                    # 调用错误处理方法
                    self._handle_paraformer_error()
            else:
                logger.debug("📝 Paraformer处于降级模式，使用千问转录作为主要来源")
            
            return qwen_success
                
        except Exception as e:
            logger.error(f"Error appending audio buffer: {e}")
            return False
    
    async def _send_audio_to_qwen_direct(self, audio_data: bytes) -> bool:
        """发送音频到千问（原有逻辑）"""
        import base64
        
        try:
            # 确保音频格式兼容
            compatible_audio = self.ensure_audio_compatibility(audio_data)
            
            # 将音频数据编码为base64
            audio_b64 = base64.b64encode(compatible_audio).decode()
            
            event = {
                "type": "input_audio_buffer.append",
                "audio": audio_b64
            }
            
            success = await self.send_event(event)
            if success:
                logger.debug(f"Audio buffer appended to Qwen: {len(audio_data)} bytes")
            
            return success
            
        except Exception as e:
            logger.error(f"Error sending audio to Qwen: {e}")
            return False

    async def _publish_paraformer_delta(self, delta: str, full_text: str, result: 'RecognitionResult') -> None:
        """发布Paraformer转录增量到消息总线 - 线程安全版本"""
        try:
            from .message_bus import message_bus, TopicNames, create_message
            from .event_types import EventType, MessageType
            from datetime import datetime
            
            # 检查是否为完整句子结束
            sentence_data = result.get_sentence()
            is_final = False
            try:
                is_final = result.is_sentence_end() if sentence_data else False
            except Exception:
                is_final = False
            
            # 发布增量事件
            delta_message = create_message(
                event_type=EventType.TRANSCRIPT_DELTA,
                message_type=MessageType.LISTENER_TRANSCRIPT,
                data={
                    "delta": delta,
                    "text": delta,
                    "session_id": self.current_session_id,
                    "source": "paraformer",
                    "is_delta": True,
                    "is_final": is_final,
                    "timestamp": datetime.now().isoformat(),
                    "confidence": sentence_data.get('confidence', 0.9) if sentence_data else 0.9
                },
                source_agent="realtime_manager_paraformer"
            )
            
            await message_bus.publish(TopicNames.TRANSCRIPT_DELTA, delta_message)
            logger.info(f"📡 Paraformer DELTA published: '{delta}'")
            
            # 如果是句子结束，发布完整转录
            if is_final:
                complete_message = create_message(
                    event_type=EventType.TRANSCRIPT_COMPLETED,
                    message_type=MessageType.LISTENER_TRANSCRIPT,
                    data={
                        "transcript": full_text,
                        "text": full_text,
                        "session_id": self.current_session_id,
                        "source": "paraformer",
                        "is_final": True,
                        "confidence": sentence_data.get('confidence', 0.95) if sentence_data else 0.95,
                        "timestamp": datetime.now().isoformat()
                    },
                    source_agent="realtime_manager_paraformer"
                )
                await message_bus.publish(TopicNames.TRANSCRIPT_COMPLETED, complete_message)
                logger.info(f"📡 Paraformer COMPLETE published: '{full_text}'")
            
        except Exception as e:
            logger.error(f"Failed to publish Paraformer delta to message bus: {e}")

    async def _publish_paraformer_complete(self, transcript: str) -> None:
        """发布Paraformer完整转录到消息总线"""
        try:
            from .message_bus import message_bus, TopicNames, create_message
            from .event_types import EventType, MessageType
            from datetime import datetime
            
            message_data = create_message(
                event_type=EventType.TRANSCRIPT_COMPLETED,
                message_type=MessageType.LISTENER_TRANSCRIPT,
                data={
                    "transcript": transcript,
                    "text": transcript,
                    "session_id": getattr(self, 'current_session_id', None),
                    "source": "paraformer",
                    "is_final": True,
                    "confidence": 0.9,
                    "timestamp": datetime.utcnow().isoformat()
                },
                source_agent="realtime_manager_paraformer"
            )
            
            await message_bus.publish(TopicNames.TRANSCRIPT_COMPLETED, message_data)
            logger.info(f"📡 Paraformer完整转录已发布: '{transcript}'")
            
        except Exception as e:
            logger.error(f"发布Paraformer完整转录失败: {e}")

    def _handle_paraformer_error(self) -> None:
        """处理Paraformer错误并触发降级"""
        self._paraformer_error_count += 1
        logger.warning(f"⚠️ Paraformer错误计数: {self._paraformer_error_count}/{self._max_paraformer_errors}")
        
        if self._paraformer_error_count >= self._max_paraformer_errors:
            logger.warning(f"🚨 Paraformer错误次数达到上限({self._max_paraformer_errors})，切换到降级模式")
            self._paraformer_fallback = True
            
            # 停止当前识别器
            if self._paraformer_recognition:
                try:
                    self._paraformer_recognition.stop()
                    logger.info("🛑 Paraformer识别器已停止")
                except Exception as e:
                    logger.error(f"停止Paraformer识别器时出错: {e}")
                finally:
                    self._paraformer_recognition = None
            
            # 记录降级时间
            import time
            self._last_paraformer_check = time.time()

    async def _check_paraformer_recovery(self) -> None:
        """检查并尝试恢复Paraformer服务"""
        import time
        
        # 如果处于降级模式，且距离上次检查已过5分钟，尝试恢复
        if (self._paraformer_fallback and 
            self._last_paraformer_check and 
            time.time() - self._last_paraformer_check > 300):  # 5分钟
            
            logger.info("🔄 尝试恢复Paraformer服务...")
            try:
                # 重置错误计数，尝试重新初始化
                self._paraformer_error_count = 0
                self._paraformer_fallback = False
                self._paraformer_recognition = None
                logger.info("✅ Paraformer服务已重置，将在下次音频处理时重新初始化")
            except Exception as e:
                logger.warning(f"⚠️ Paraformer服务恢复失败: {e}")
                self._paraformer_fallback = True
                self._last_paraformer_check = time.time()

    async def commit_audio_buffer(self) -> bool:
        """
        新增：提交音频缓冲区（千问特有）
        
        Returns:
            bool: 发送是否成功
        """
        if not self.is_connected:
            logger.error("Cannot commit audio buffer: not connected")
            return False
            
        try:
            event = {
                "type": "input_audio_buffer.commit"
            }
            
            return await self.send_event(event)
            
        except Exception as e:
            logger.error(f"Error committing audio buffer: {e}")
            return False
    
    async def send_image_buffer_append(self, image_data: bytes) -> bool:
        """
        新增：向图像缓冲区追加图像数据（千问特有，未来扩展）
        
        Args:
            image_data: JPEG格式的图像数据
            
        Returns:
            bool: 发送是否成功
        """
        import base64
        
        if not self.is_connected:
            logger.error("Cannot append image buffer: not connected")
            return False
            
        try:
            # 将图像数据编码为base64
            image_b64 = base64.b64encode(image_data).decode()
            
            event = {
                "type": "input_image_buffer.append", 
                "image": image_b64
            }
            
            return await self.send_event(event)
            
        except Exception as e:
            logger.error(f"Error appending image buffer: {e}")
            return False
    
    async def create_response(self) -> bool:
        """
        新增：请求创建回复（千问特有，手动模式）
        
        Returns:
            bool: 发送是否成功
        """
        if not self.is_connected:
            logger.error("Cannot create response: not connected")
            return False

        # 🎯 新增：状态守护检查
        if self.is_response_active:
            logger.warning("Cannot create a new response because another response is already active. Ignoring request.")
            return False
            
        try:
            event = {
                "type": "response.create",
                "response": {
                    "modalities": ["text", "audio"]
                }
            }
            
            success = await self.send_event(event)
            if success:
                self.is_response_active = True  # 标记响应激活
            return success
            
        except Exception as e:
            logger.error(f"Error creating response: {e}")
            return False
    
    async def cancel_response(self) -> bool:
        """
        新增：取消当前回复（千问特有）
        
        Returns:
            bool: 发送是否成功
        """
        if not self.is_connected:
            logger.error("Cannot cancel response: not connected")
            return False
            
        try:
            event = {
                "type": "response.cancel"
            }
            
            # --- START OF FIX ---
            # 预先设置状态，防止竞态条件
            self.is_response_active = False
            logger.info("🔄 preemptively set is_response_active to False.")
            # --- END OF FIX ---
            
            return await self.send_event(event)
            
        except Exception as e:
            logger.error(f"Error canceling response: {e}")
            return False
    
    async def send_text_input(self, text: str) -> bool:
        """
        新增：发送一个文本输入项来触发AI响应。
        这是在主动打断后强制AI说话的可靠方法。
        
        Args:
            text: 要发送的文本。在打断场景中，可以是一个空字符串。
            
        Returns:
            bool: 发送是否成功
        """
        if not self.is_connected:
            logger.error("Cannot send text input: not connected")
            return False

        # 🎯 增强：详细的状态检查和日志
        if self.is_response_active:
            logger.warning(f"Cannot send text input, a response is already active. Current response state: {self.is_response_active}")
            return False
            
        try:
            # 记录发送前的状态
            logger.info(f"🎯 Attempting to send text input to trigger AI response. Text: '{text}', Current API state: connected={self.is_connected}, session_active={self.is_session_active}")
            
            event = {
                "type": "conversation.item.create",
                "item": {
                    "type": "message",
                    "role": "user",
                    "content": [
                        {
                            "type": "input_text",
                            "text": text
                        }
                    ]
                }
            }
            
            success = await self.send_event(event)
            if success:
                self.is_response_active = True  # 标记响应已激活
                logger.info(f"✅ Text input sent successfully to trigger response: '{text}'. Response state set to active.")
            else:
                logger.error(f"❌ Failed to send text input event to API. Text: '{text}'")

            return success
            
        except Exception as e:
            logger.error(f"💥 Exception occurred while sending text input: {e}", exc_info=True)
            return False
    
    def configure_vad(self, threshold: float = 0.5, prefix_padding_ms: int = 300, 
                     silence_duration_ms: int = 800, create_response: bool = True, 
                     interrupt_response: bool = True) -> Dict[str, Any]:
        """
        🎯 DEPRECATED: 此方法已弃用，VAD配置现在由VAD管理器统一管理
        配置语音活动检测(VAD)参数 - 仅在VAD管理器不可用时作为后备
        
        Args:
            threshold: VAD检测阈值 (0.0-1.0)
            prefix_padding_ms: 语音开始前的填充时间(毫秒)
            silence_duration_ms: 静音持续时间阈值(毫秒)
            create_response: 检测到语音结束时是否自动创建响应
            interrupt_response: 是否允许中断当前响应
            
        Returns:
            Dict[str, Any]: VAD配置字典
        """
        # 尝试从VAD管理器获取配置
        try:
            from core.vad_manager import get_vad_manager
            vad_manager = get_vad_manager()
            if vad_manager:
                return vad_manager.get_config().to_qwen_config()
        except:
            pass
        
        # 后备硬编码配置（已废弃）
        vad_config = {
            "type": "server_vad",
            "threshold": max(-1.0, min(1.0, threshold)),  # 千问支持负值
            "prefix_padding_ms": max(0, prefix_padding_ms),
            "silence_duration_ms": max(100, silence_duration_ms),  # 最小100ms
            "create_response": create_response,
            "interrupt_response": interrupt_response
        }
        
        logger.info(f"VAD configured: threshold={vad_config['threshold']}, "
                   f"silence_duration={vad_config['silence_duration_ms']}ms")
        
        return vad_config
    
    async def update_vad_settings(self, **vad_params) -> bool:
        """
        动态更新VAD设置
        
        Args:
            **vad_params: VAD参数 (threshold, prefix_padding_ms, etc.)
            
        Returns:
            bool: 更新是否成功
        """
        try:
            new_vad_config = self.configure_vad(**vad_params)
            
            # 更新会话配置中的VAD设置
            success = await self.update_session_config({
                "turn_detection": new_vad_config
            })
            
            if success:
                logger.info("VAD settings updated successfully")
            else:
                logger.error("Failed to update VAD settings")
                
            return success
            
        except Exception as e:
            logger.error(f"Error updating VAD settings: {e}")
            return False
    
    async def enable_manual_mode(self) -> bool:
        """
        切换到手动模式（禁用VAD自动检测）
        
        Returns:
            bool: 切换是否成功
        """
        try:
            manual_config = {
                "turn_detection": {
                    "type": "manual"
                }
            }
            
            success = await self.update_session_config(manual_config)
            if success:
                logger.info("Switched to manual mode (VAD disabled)")
            
            return success
            
        except Exception as e:
            logger.error(f"Error switching to manual mode: {e}")
            return False
    
    async def enable_server_vad(self, **vad_params) -> bool:
        """
        启用服务端VAD检测模式
        
        Args:
            **vad_params: VAD参数
            
        Returns:
            bool: 启用是否成功
        """
        try:
            vad_config = self.configure_vad(**vad_params)
            
            success = await self.update_session_config({
                "turn_detection": vad_config
            })
            
            if success:
                logger.info("Server VAD enabled successfully")
            
            return success
            
        except Exception as e:
            logger.error(f"Error enabling server VAD: {e}")
            return False
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.disconnect()
        return False

    def _analyze_qwen_error(self, error_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析千问API特有错误并提供恢复建议
        
        Args:
            error_data: 错误数据
            
        Returns:
            Dict[str, Any]: 错误分析结果和恢复建议
        """
        error_code = error_data.get("code", "unknown")
        error_message = error_data.get("message", "")
        error_type = error_data.get("type", "unknown")
        
        analysis = {
            "code": error_code,
            "message": error_message,
            "type": error_type,
            "severity": "medium",
            "recoverable": True,
            "suggested_action": "retry",
            "recovery_delay": 2.0
        }
        
        # 千问API特有错误码处理
        if error_code == "invalid_request_error":
            analysis.update({
                "severity": "high",
                "recoverable": False,
                "suggested_action": "fix_request",
                "description": "请求格式错误，需要检查参数"
            })
        elif error_code == "authentication_error":
            analysis.update({
                "severity": "critical",
                "recoverable": False,
                "suggested_action": "check_credentials",
                "description": "认证失败，请检查API Key"
            })
        elif error_code == "rate_limit_error":
            analysis.update({
                "severity": "medium",
                "recoverable": True,
                "suggested_action": "wait_retry",
                "recovery_delay": 60.0,
                "description": "触发速率限制，需要等待后重试"
            })
        elif error_code == "server_error":
            analysis.update({
                "severity": "high",
                "recoverable": True,
                "suggested_action": "retry_with_backoff",
                "recovery_delay": 10.0,
                "description": "服务器内部错误，建议重试"
            })
        elif error_code == "model_error":
            analysis.update({
                "severity": "high",
                "recoverable": True,
                "suggested_action": "restart_session",
                "recovery_delay": 5.0,
                "description": "模型处理错误，建议重启会话"
            })
        elif "audio" in error_message.lower():
            analysis.update({
                "severity": "medium",
                "recoverable": True,
                "suggested_action": "check_audio_format",
                "description": "音频格式相关错误，检查音频数据"
            })
        elif "vad" in error_message.lower():
            analysis.update({
                "severity": "low",
                "recoverable": True,
                "suggested_action": "adjust_vad_settings",
                "description": "VAD相关错误，建议调整VAD参数"
            })
        
        return analysis
    
    async def _handle_api_error(self, error_data: Dict[str, Any]) -> bool:
        """
        处理API错误并尝试自动恢复
        
        Args:
            error_data: 错误数据
            
        Returns:
            bool: 是否成功处理错误
        """
        try:
            # 分析错误
            analysis = self._analyze_qwen_error(error_data)
            
            logger.error(f"千问API错误: {analysis['code']} - {analysis['message']}")
            logger.info(f"错误分析: {analysis.get('description', '未知错误')}")
            logger.info(f"建议操作: {analysis['suggested_action']}")
            
            # 🎯 新增：在API错误时清理可能的状态不一致
            error_message = error_data.get("message", "")
            if "already has an active response" in error_message or "none active response" in error_message:
                logger.info("🔄 Clearing response state due to API state conflict")
                self.is_response_active = False
            
            # 记录错误统计
            self.last_error = Exception(f"API Error: {analysis['code']} - {analysis['message']}")
            
            # 触发错误事件
            await self._dispatch_event("api.error", {
                "error_data": error_data,
                "analysis": analysis,
                "timestamp": datetime.utcnow().isoformat()
            })
            
            # 根据错误类型执行恢复操作
            if analysis["recoverable"]:
                recovery_success = await self._attempt_error_recovery(analysis)
                if recovery_success:
                    logger.info(f"错误恢复成功: {analysis['suggested_action']}")
                    return True
                else:
                    logger.error(f"错误恢复失败: {analysis['suggested_action']}")
            else:
                logger.error(f"不可恢复的错误: {analysis['description']}")
                
            return False
            
        except Exception as e:
            logger.error(f"处理API错误时发生异常: {e}")
            return False
    
    async def _attempt_error_recovery(self, analysis: Dict[str, Any]) -> bool:
        """
        尝试根据错误分析执行恢复操作
        
        Args:
            analysis: 错误分析结果
            
        Returns:
            bool: 恢复是否成功
        """
        try:
            action = analysis["suggested_action"]
            delay = analysis.get("recovery_delay", 2.0)
            
            if action == "retry":
                logger.info(f"等待 {delay} 秒后重试...")
                await asyncio.sleep(delay)
                return True
                
            elif action == "wait_retry":
                logger.info(f"由于速率限制，等待 {delay} 秒...")
                await asyncio.sleep(delay)
                return True
                
            elif action == "retry_with_backoff":
                logger.info(f"使用退避策略，等待 {delay} 秒...")
                await asyncio.sleep(delay)
                return True
                
            elif action == "restart_session":
                logger.info("尝试重启会话...")
                if self.current_session_config:
                    # 保存当前配置
                    old_config = self.current_session_config.copy()
                    
                    # 停止当前会话
                    await self.disconnect()
                    await asyncio.sleep(2.0)
                    
                    # 重新连接
                    if await self.connect():
                        # 恢复会话配置
                        return await self.start_session(old_config)
                return False
                
            elif action == "adjust_vad_settings":
                logger.info("尝试调整VAD设置...")
                # 使用更保守的VAD设置
                return await self.update_vad_settings(
                    threshold=0.7,  # 提高阈值
                    silence_duration_ms=1000  # 增加静音时间
                )
                
            elif action == "check_audio_format":
                logger.info("音频格式错误，记录但继续处理...")
                return True
                
            else:
                logger.warning(f"未知的恢复操作: {action}")
                return False
                
        except Exception as e:
            logger.error(f"执行错误恢复时发生异常: {e}")
            return False

    async def _handle_response_audio_transcript_delta(self, event_type: str, data: Dict[str, Any]) -> None:
        """处理AI回复音频转录增量事件 - 修复版：支持流式文本显示"""
        try:
            from .message_bus import message_bus, TopicNames, create_message
            from .event_types import EventType, MessageType

            delta = data.get("delta", "")
            response_id = data.get("response_id", "unknown")

            if delta:
                logger.debug(f"🎵 AI音频转录增量: '{delta}'")

                # 累积转录文本
                if response_id not in self.response_transcripts:
                    self.response_transcripts[response_id] = ""

                self.response_transcripts[response_id] += delta

                logger.debug(f"📝 累积转录文本: '{self.response_transcripts[response_id]}'")

                # 🎯 修复：发布AI响应增量到前端，实现流式文本效果
                ai_response_message = create_message(
                    event_type=EventType.RESPONSE_GENERATED,
                    message_type=MessageType.SPEAKER_AUDIO,
                    data={
                        "text": delta,  # 发送增量文本
                        "content": delta,
                        "transcript": delta,
                        "response_text": delta,
                        "response_id": response_id,
                        "is_final": False,
                        "is_delta": True,
                        "complete": False,
                        "streaming": True,
                        "source": "ai_response_delta",
                        "session_id": getattr(self, 'current_session_id', None),
                        "timestamp": data.get("timestamp")
                    },
                    source_agent="realtime_manager"
                )

                await message_bus.publish(TopicNames.RESPONSE_EVENTS, ai_response_message)
                logger.debug(f"📡 AI响应增量已发布到前端: '{delta}'")

                # 同时发布到AI_TRANSCRIPT_EVENTS供Strategic Thinker订阅
                ai_transcript_message = create_message(
                    event_type=EventType.TRANSCRIPT_DELTA,
                    message_type=MessageType.SPEAKER_AUDIO,
                    data={
                        "transcript": delta,
                        "text": delta,
                        "response_id": response_id,
                        "is_final": False,
                        "is_delta": True,
                        "source": "ai_response",
                        "timestamp": data.get("timestamp")
                    },
                    source_agent="realtime_manager"
                )

                await message_bus.publish(TopicNames.AI_TRANSCRIPT_EVENTS, ai_transcript_message)

        except Exception as e:
            logger.error(f"Error handling AI response audio transcript delta: {e}")
    
    async def _handle_response_audio_transcript_done(self, event_type: str, data: Dict[str, Any]) -> None:
        """处理AI回复音频转录完成事件 - 修复版：不再发送到前端，由response.done统一处理"""
        try:
            from .message_bus import message_bus, TopicNames, create_message
            from .event_types import EventType, MessageType
            
            transcript = data.get("transcript", "")
            response_id = data.get("response_id", "unknown")
            
            if transcript:
                logger.info(f"🎵 AI音频转录完成: '{transcript}'")
                
                # 🎯 核心修复：只记录日志，不再发送到前端
                # 最终的AI回复将由 _handle_response_done 方法统一发送
                logger.debug(f"📝 转录完成，等待 response.done 事件统一发送")
                
                # 🎯 新增：发布完整的AI转录到AI_TRANSCRIPT_EVENTS供Strategic Thinker订阅
                ai_transcript_complete_message = create_message(
                    event_type=EventType.TRANSCRIPT_COMPLETED,
                    message_type=MessageType.SPEAKER_AUDIO,
                    data={
                        "transcript": transcript,
                        "text": transcript,
                        "response_id": response_id,
                        "is_final": True,
                        "complete": True,
                        "source": "ai_response",
                        "timestamp": data.get("timestamp")
                    },
                    source_agent="realtime_manager"
                )
                
                await message_bus.publish(TopicNames.AI_TRANSCRIPT_EVENTS, ai_transcript_complete_message)
                
        except Exception as e:
            logger.error(f"Error handling AI response audio transcript done: {e}")
    
    async def _handle_response_done(self, event_type: str, data: Dict[str, Any]) -> None:
        """处理响应完成事件 - 发送流式响应完成标记"""
        try:
            from .message_bus import message_bus, TopicNames, create_message
            from .event_types import EventType, MessageType

            response = data.get("response", {})
            response_id = response.get("id")
            status = response.get("status", "unknown")
            
            # 🎯 核心修复：如果响应状态是 'cancelled'，则直接忽略，不发布最终文本
            if status == "cancelled":
                logger.info(f"Ignoring response.done for cancelled response_id: {response_id}")
                return
            
            full_transcript = ""
            
            # 1. 优先从response.done事件的output中获取最权威的最终文本
            try:
                if response.get("output"):
                    full_transcript = response["output"][0]["content"][0]["transcript"]
            except (IndexError, KeyError):
                logger.warning(f"无法从response.done事件中解析最终转录，response_id: {response_id}")
                # 2. 如果解析失败，才使用我们自己累积的文本作为备用
                full_transcript = self.response_transcripts.get(response_id, "")

            logger.debug(f"AI响应完成，response_id: {response_id}")

            # 🎯 修复：发送最终完整文本，确保不遗漏最后的delta
            # 优先使用response.done事件中的权威文本，如果没有则使用累积的文本
            completion_message = create_message(
                event_type=EventType.RESPONSE_GENERATED,
                message_type=MessageType.SPEAKER_AUDIO,
                data={
                    "text": full_transcript,  # 发送最终完整文本，确保不遗漏
                    "content": full_transcript,
                    "response_id": response_id,
                    "is_final": True,
                    "is_delta": False,
                    "complete": True,
                    "streaming": False,
                    "source": "ai_response_complete",
                    "session_id": getattr(self, 'current_session_id', None),
                    "timestamp": data.get("timestamp"),
                    "message_key": f"{response_id}_complete"
                },
                source_agent="realtime_manager"
            )

            await message_bus.publish(TopicNames.RESPONSE_EVENTS, completion_message)
            logger.info(f"📡 AI响应完成标记已发布: response_id={response_id}")

            # 清理累积的转录文本
            if response_id in self.response_transcripts:
                del self.response_transcripts[response_id]

        except Exception as e:
            logger.error(f"Error handling response done: {e}")
    
    async def _handle_response_audio_delta(self, event_type: str, data: Dict[str, Any]) -> None:
        """处理AI回复音频增量事件 - 修复重复播放问题"""
        try:
            from .message_bus import message_bus, TopicNames, create_message
            from .event_types import EventType, MessageType
            
            # 检查是否已被中断
            if self.audio_interrupted:
                logger.debug("🛑 Audio output interrupted, skipping delta")
                return
            
            # 标记正在输出音频
            self.is_outputting_audio = True
            
            # 获取响应ID
            response_id = data.get("response_id")
            if response_id and not self.current_audio_response_id:
                self.current_audio_response_id = response_id
                logger.info(f"🎵 Started audio output for response: {response_id}")
            
            audio_delta = data.get("delta", "")
            if audio_delta:
                logger.debug(f"🔊 AI音频增量接收: {len(audio_delta)} 字符 (base64)")
                
                # 验证base64格式
                try:
                    import base64
                    decoded_audio = base64.b64decode(audio_delta)
                    logger.debug(f"🎼 解码后音频长度: {len(decoded_audio)} bytes")
                    
                    # 缓存音频数据（用于可能的重播）
                    self.audio_output_buffer.append({
                        "data": audio_delta,
                        "timestamp": data.get("timestamp"),
                        "response_id": response_id
                    })
                    
                except Exception as decode_error:
                    logger.error(f"❌ base64解码失败: {decode_error}")
                    return
                
                # 🎯 修复：统一音频通道处理逻辑
                audio_sent = False
                
                # 优先使用直接音频通道
                if self.direct_audio_enabled and self.direct_connections:
                    direct_sends = await self.send_direct_audio(
                        audio_data=audio_delta,
                        format="pcm16",
                        sample_rate=24000,
                        response_id=response_id
                    )
                    
                    if direct_sends > 0:
                        logger.debug(f"✅ 直接音频通道发送成功: {direct_sends} 个连接")
                        audio_sent = True
                
                # 如果直接通道失败或不可用，且没有检测到用户语音，使用消息总线
                if not audio_sent and not self.speech_detected:
                    message_data = create_message(
                        event_type=EventType.AUDIO_OUTPUT,
                        message_type=MessageType.SPEAKER_AUDIO,
                        data={
                            "audio_data": audio_delta,
                            "format": "pcm16",
                            "sample_rate": 24000,
                            "is_delta": True,
                            "timestamp": data.get("timestamp"),
                            "response_id": response_id,
                            "source": "message_bus_primary",  # 明确标识为主要通道
                            "chunk_id": f"{response_id}_{len(self.audio_output_buffer)}"  # 唯一标识符
                        },
                        source_agent="realtime_manager"
                    )
                    
                    await message_bus.publish(TopicNames.AUDIO_OUTPUT, message_data)
                    logger.debug(f"📡 AI音频增量已发布到消息总线")
                    audio_sent = True
                
                if not audio_sent:
                    if self.speech_detected:
                        logger.debug("🎤 Speech detected, buffering audio")
                    else:
                        logger.warning("⚠️ No audio channel available for output")
                
        except Exception as e:
            logger.error(f"Error handling AI response audio delta: {e}")
            self.is_outputting_audio = False
    
    async def _handle_response_audio_done(self, event_type: str, data: Dict[str, Any]) -> None:
        """处理AI回复音频完成事件"""
        try:
            from .message_bus import message_bus, TopicNames, create_message
            from .event_types import EventType, MessageType
            
            response_id = data.get("response_id")
            logger.info(f"🎵 AI音频输出完成: {response_id}")
            
            # 重置音频输出状态
            self.is_outputting_audio = False
            self.audio_interrupted = False
            
            if response_id == self.current_audio_response_id:
                self.current_audio_response_id = None
                
            # 发布音频完成消息
            message_data = create_message(
                event_type=EventType.AUDIO_OUTPUT,
                message_type=MessageType.SPEAKER_AUDIO,
                data={
                    "audio_complete": True,
                    "response_id": response_id,
                    "timestamp": data.get("timestamp"),
                    "buffer_size": len(self.audio_output_buffer)
                },
                source_agent="realtime_manager"
            )
            
            await message_bus.publish(TopicNames.AUDIO_OUTPUT, message_data)
            logger.debug(f"📡 AI音频完成事件已发布")
            
            # 清理音频缓冲区
            self.audio_output_buffer.clear()
            
        except Exception as e:
            logger.error(f"Error handling AI response audio done: {e}")
            self.is_outputting_audio = False

    async def _handle_response_output_item_done(self, event_type: str, data: Dict[str, Any]) -> None:
        """处理AI回复输出项完成事件"""
        try:
            item = data.get("item", {})
            item_id = item.get("id", "unknown")
            item_type = item.get("type", "unknown") 
            
            logger.debug(f"🎯 AI输出项完成: {item_id} (类型: {item_type})")
            
        except Exception as e:
            logger.error(f"Error handling response output item done: {e}")

    async def _handle_response_content_part_done(self, event_type: str, data: Dict[str, Any]) -> None:
        """处理AI回复内容部分完成事件"""
        try:
            part = data.get("part", {})
            part_type = part.get("type", "unknown")
            
            logger.debug(f"🎯 AI内容部分完成: 类型={part_type}")
            
        except Exception as e:
            logger.error(f"Error handling response content part done: {e}")

    async def _handle_input_audio_transcription_delta(self, event_type: str, data: Dict[str, Any]) -> None:
        """处理用户输入音频转录增量事件 - 已禁用：Delta全部交给Paraformer处理"""
        try:
            delta = data.get("delta", "")
            item_id = data.get("item_id", "unknown")
            
            if delta:
                # 🎯 核心变更：不再发布Qwen的delta事件，Delta全部交给Paraformer处理
                logger.debug(f"📝 Qwen音频转录增量(不发布): '{delta}' (item: {item_id})")
                logger.debug("🎯 Delta事件已交由Paraformer处理，Qwen delta不再发布到消息总线")
                
        except Exception as e:
            logger.error(f"Error handling input audio transcription delta: {e}")
    
    async def _handle_input_audio_transcription_completed(self, event_type: str, data: Dict[str, Any]) -> None:
        """处理用户输入音频转录完成事件 - 修复版：此事件不应再触发AI响应"""
        try:
            from .message_bus import message_bus, TopicNames, create_message
            from .event_types import EventType, MessageType
            
            transcript = data.get("transcript", "")
            item_id = data.get("item_id", "unknown")
            
            if transcript:
                logger.info(f"📝 用户音频转录完成: '{transcript}' (item: {item_id})")
                
                # 🎯 核心修改点：Qwen complete作为Paraformer的降级备选
                # 用户转录完成 -> 发布用户转录事件，标识为qwen降级源
                message_data = create_message(
                    event_type=EventType.TRANSCRIPT_COMPLETED,
                    message_type=MessageType.LISTENER_TRANSCRIPT,
                    data={
                        "transcript": transcript,
                        "text": transcript,
                        "item_id": item_id,
                        "session_id": getattr(self, 'current_session_id', None),
                        "source": "qwen",  # 标识为千问降级源
                        "is_final": True,
                        "confidence": 0.8,  # 略低于Paraformer置信度
                        "timestamp": data.get("timestamp")
                    },
                    source_agent="realtime_manager_qwen"
                )
                
                await message_bus.publish(TopicNames.TRANSCRIPT_COMPLETED, message_data)
                logger.debug(f"📡 用户完整转录已发布到消息总线: '{transcript}'")
                
                # 🎯 移除冗余的调试消息发布，避免SYSTEM_NOTIFICATION错误
                # 用户转录完成的主要逻辑已由上面的publish完成
                # 不再需要额外的调试消息，避免日志噪音
                logger.debug(f"📝 用户转录处理完成: '{transcript}' (item: {item_id})")
                
        except Exception as e:
            logger.error(f"Error handling input audio transcription completed: {e}")

    async def _handle_speech_started(self, event_type: str, data: Dict[str, Any]) -> None:
        """
        处理千问API的input_audio_buffer.speech_started事件
        这是barge-in功能的关键入口点
        """
        try:
            logger.info("🎤 千问VAD检测到用户开始说话")
            
            # 调用快速通道barge-in逻辑
            await self.handle_speech_detection(speech_started=True)
            
        except Exception as e:
            logger.error(f"Error handling speech started event: {e}")

    async def _handle_speech_stopped(self, event_type: str, data: Dict[str, Any]) -> None:
        """
        处理千问API的input_audio_buffer.speech_stopped事件
        """
        try:
            logger.info("🔇 千问VAD检测到用户停止说话")
            
            # 调用语音停止处理逻辑
            await self.handle_speech_detection(speech_started=False)
            
        except Exception as e:
            logger.error(f"Error handling speech stopped event: {e}")

    async def register_websocket_connection(self, session_id: str, websocket: Any) -> bool:
        """
        注册直接WebSocket连接用于音频数据推送
        
        Args:
            session_id: 会话ID
            websocket: WebSocket连接对象
            
        Returns:
            bool: 注册是否成功
        """
        try:
            connection = DirectAudioConnection(
                session_id=session_id,
                websocket=websocket,
                last_activity=datetime.utcnow()
            )
            
            self.direct_connections[session_id] = connection
            logger.info(f"🔗 直接音频连接已注册: session={session_id}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 注册直接音频连接失败: session={session_id}, error={e}")
            return False
    
    async def unregister_websocket_connection(self, session_id: str) -> bool:
        """
        注销直接WebSocket连接
        
        Args:
            session_id: 会话ID
            
        Returns:
            bool: 注销是否成功
        """
        try:
            if session_id in self.direct_connections:
                del self.direct_connections[session_id]
                logger.info(f"🗑️ 直接音频连接已注销: session={session_id}")
                return True
            else:
                logger.warning(f"⚠️ 尝试注销不存在的连接: session={session_id}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 注销直接音频连接失败: session={session_id}, error={e}")
            return False
    
    async def send_direct_audio(self, audio_data: str, format: str = "pcm16", 
                               sample_rate: int = 24000, response_id: str = None) -> int:
        """
        直接发送音频数据到所有活跃的WebSocket连接
        
        Args:
            audio_data: base64编码的音频数据
            format: 音频格式
            sample_rate: 采样率
            response_id: 响应ID
            
        Returns:
            int: 成功发送的连接数量
        """
        if not self.direct_audio_enabled or not audio_data:
            return 0
        
        successful_sends = 0
        failed_connections = []
        
        for session_id, connection in self.direct_connections.items():
            try:
                # 检查连接是否仍然活跃
                if not connection.is_active():
                    failed_connections.append(session_id)
                    continue
                
                # 构建直接音频消息
                direct_message = {
                    "type": "direct_audio_output",
                    "data": {
                        "audio_data": audio_data,
                        "format": format,
                        "sample_rate": sample_rate,
                        "session_id": session_id,
                        "response_id": response_id,
                        "timestamp": datetime.utcnow().isoformat(),
                        "source": "direct_channel"
                    }
                }
                
                # 直接发送到WebSocket
                await connection.websocket.send_text(json.dumps(direct_message))
                
                # 更新连接活动时间
                connection.last_activity = datetime.utcnow()
                successful_sends += 1
                
                logger.debug(f"🎵 直接音频数据已发送: session={session_id}, size={len(audio_data)}")
                
            except Exception as e:
                logger.warning(f"⚠️ 直接音频发送失败: session={session_id}, error={e}")
                failed_connections.append(session_id)
        
        # 清理失败的连接
        for session_id in failed_connections:
            await self.unregister_websocket_connection(session_id)
        
        if successful_sends > 0:
            logger.info(f"🎵 直接音频通道发送成功: {successful_sends} 个连接")
        
        return successful_sends
    
    def get_direct_connections_status(self) -> Dict[str, Any]:
        """
        获取直接连接状态
        
        Returns:
            Dict: 连接状态信息
        """
        active_connections = 0
        inactive_connections = 0
        
        for connection in self.direct_connections.values():
            if connection.is_active():
                active_connections += 1
            else:
                inactive_connections += 1
        
        return {
            "direct_audio_enabled": self.direct_audio_enabled,
            "total_connections": len(self.direct_connections),
            "active_connections": active_connections,
            "inactive_connections": inactive_connections,
            "connection_details": [
                {
                    "session_id": conn.session_id,
                    "is_active": conn.is_active(),
                    "last_activity": conn.last_activity.isoformat(),
                    "audio_format": conn.audio_format,
                    "sample_rate": conn.sample_rate
                }
                for conn in self.direct_connections.values()
            ]
        }

    def set_current_session_id(self, session_id: str) -> None:
        """
        设置当前用户会话ID
        
        Args:
            session_id: 用户WebSocket会话ID
        """
        self.current_session_id = session_id
        logger.debug(f"🎯 RealtimeManager设置当前会话ID: {session_id}")

    def clear_current_session_id(self) -> None:
        """清除当前用户会话ID"""
        old_session_id = self.current_session_id
        self.current_session_id = None
        if old_session_id:
            logger.debug(f"🎯 RealtimeManager清除会话ID: {old_session_id}")


# 全局实例（可选）
_global_manager: Optional[RealtimeManager] = None


def get_global_manager() -> Optional[RealtimeManager]:
    """获取全局连接管理器实例"""
    return _global_manager


def set_global_manager(manager: RealtimeManager) -> None:
    """设置全局连接管理器实例"""
    global _global_manager
    _global_manager = manager 