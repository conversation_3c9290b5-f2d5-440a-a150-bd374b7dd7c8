# Fully refactored agents/strategic_thinker.py

import asyncio
import json
from typing import Dict, Any, List
from loguru import logger
from datetime import datetime
from thefuzz import fuzz

from core.message_bus import message_bus, TopicNames, create_message
from core.event_types import EventType, MessageType
from core.config import settings
from core.base_agent import BaseAgent
from core.client_factory import ClientFactory

class StrategicThinker(BaseAgent):
    """
    Strategic Thinker for deep, context-aware analysis to manage conversation flow.
    Focuses on identifying disruptive interruption opportunities like verbosity and repetition.
    """
    
    def __init__(self):
        super().__init__("strategic_thinker")
        self.agent_id = "strategic_thinker"  # 添加agent_id属性
        self.client = None
        self.conversation_context: Dict[str, List[Dict[str, Any]]] = {}
        self.delta_buffers: Dict[str, str] = {}
        self.analysis_tasks: Dict[str, asyncio.Task] = {}
        self.buffer_min_length = settings.strategic_buffer_min_length
        self.buffer_max_length = settings.strategic_buffer_max_length
        self.disruptive_threshold = settings.disruptive_interruption_threshold
        
    async def initialize(self) -> bool:
        """Initializes client and subscribes to all necessary transcript topics."""
        try:
            self.client = await ClientFactory.get_doubao_client()
            await message_bus.connect()
            
            await message_bus.subscribe(TopicNames.TRANSCRIPT_DELTA, self.handle_transcript_delta)
            await message_bus.subscribe(TopicNames.TRANSCRIPT_COMPLETED, self.handle_transcript_completed)
            await message_bus.subscribe(TopicNames.AI_TRANSCRIPT_EVENTS, self.handle_ai_transcript)
            
            self.logger.info("Strategic Thinker initialized with full context awareness.")
            return True
        except Exception as e:
            self.logger.error(f"Strategic Thinker initialization failed: {e}")
            return False

    async def start(self) -> bool:
        """启动Strategic Thinker agent."""
        try:
            self.logger.info("Starting Strategic Thinker agent...")
            # Strategic Thinker是事件驱动的，不需要额外的启动逻辑
            # 所有必要的订阅已在initialize()中完成
            
            # 设置运行状态标志
            self.is_running = True
            
            self.logger.info("Strategic Thinker agent started successfully")
            return True
        except Exception as e:
            self.logger.error(f"Failed to start Strategic Thinker: {e}")
            self.is_running = False
            return False

    async def shutdown(self) -> None:
        """关闭Strategic Thinker agent并清理资源."""
        try:
            self.logger.info("Shutting down Strategic Thinker agent...")
            
            # 取消所有正在进行的分析任务
            for session_id, task in list(self.analysis_tasks.items()):
                if not task.done():
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass
            self.analysis_tasks.clear()
            
            # 清理会话上下文和缓冲区
            self.conversation_context.clear()
            self.delta_buffers.clear()
            
            # 关闭AI客户端连接
            if self.client:
                # ClientFactory会处理连接的清理
                self.client = None
            
            self.logger.info("Strategic Thinker shutdown complete")
            
        except Exception as e:
            self.logger.error(f"Error during Strategic Thinker shutdown: {e}")

    async def _append_to_session_context(self, session_id: str, role: str, content: str):
        """Appends a message to context and manages memory with a sliding window."""
        if session_id not in self.conversation_context:
            self.conversation_context[session_id] = []
        
        self.conversation_context[session_id].append({"role": role, "content": content})
        
        if len(self.conversation_context[session_id]) > 20:
            self.conversation_context[session_id] = self.conversation_context[session_id][-20:]

    async def handle_ai_transcript(self, topic: str, message: Dict[str, Any]):
        data = message.get("data", {})
        session_id = data.get("session_id")
        if session_id and (data.get("is_final") or data.get("complete")):
            transcript = data.get("transcript", "")
            if transcript:
                await self._append_to_session_context(session_id, "assistant", transcript)

    async def handle_transcript_delta(self, topic: str, message: Dict[str, Any]):
        # 🎯 新增入口日志: 这是最重要的诊断信息
        self.logger.info(f"✅ STRATEGIC_THINKER: handle_transcript_delta CALLED. Topic: {topic}, Message Source: {message.get('data', {}).get('source')}")
        
        data = message.get("data", {})
        session_id = data.get("session_id")
        delta = data.get("delta", "")
        source = data.get("source", "qwen")  # 默认为千问源
        
        if not session_id or not delta:
            # 🎯 新增日志: 记录因何提前返回
            self.logger.warning(f"⚠️ STRATEGIC_THINKER: handle_transcript_delta returning early. session_id: {session_id}, delta: '{delta}'")
            return

        # 🎯 源识别调试日志
        self.logger.info(f"🧠 StrategicThinker received DELTA from {source} for session {session_id}: '{delta}'")

        # 🎯 CORE FIX: 实现真正的实时分析 - 移除延迟逻辑，立即进行深度分析
        # 不再区分数据源优先级，所有delta都进行实时分析
        buffer = self.delta_buffers.get(session_id, "") + delta
        self.delta_buffers[session_id] = buffer

        # 🎯 CRITICAL: 大幅降低触发阈值，实现真正的实时中断
        # 每当累积足够的词语就立即分析，而不是等待用户停止说话
        words_count = len(buffer.split())
        chars_count = len(buffer)
        
        # 🎯 激进的实时分析：5个词或30个字符就开始分析
        if words_count >= 5 or chars_count >= 30:
            self.logger.info(f"🚀 REAL-TIME ANALYSIS triggered for {source} source (words={words_count}, chars={chars_count})")
            await self._trigger_analysis(buffer, session_id, source=source)
        else:
            self.logger.debug(f"📝 STRATEGIC: Buffer too short for analysis (words={words_count}, chars={chars_count})")

    async def handle_transcript_completed(self, topic: str, message: Dict[str, Any]):
        data = message.get("data", {})
        session_id = data.get("session_id")
        transcript = data.get("transcript", "")
        source = data.get("source", "paraformer")  
        
        # 🎯 新增调试日志
        self.logger.info(f"🧠 StrategicThinker received COMPLETED from {source} for session {session_id}: '{transcript}'")
        
        if session_id and transcript:
            # 🎯 优先级处理：优先处理Paraformer的complete事件
            if source == "paraformer":
                self.logger.info(f"🎯 STRATEGIC: Processing Paraformer complete (priority): '{transcript}'")
                await self._append_to_session_context(session_id, "user", transcript)
                
                # 实时分析已经在delta中进行，这里只做最后的补充检查
                if session_id in self.delta_buffers:
                    buffer = self.delta_buffers[session_id]
                    if buffer and len(buffer.split()) >= 3:
                        self.logger.info(f"🎯 Final analysis for remaining buffer: '{buffer}'")
                        await self._trigger_analysis(buffer, session_id, source="paraformer_final")
                    self.delta_buffers[session_id] = ""  # 清空buffer
                    
            elif source == "qwen":
                # 只有当没有处理过Paraformer事件时，才使用千问降级
                context_sessions = [ctx.get("session_id") for ctx in self.conversation_context.get(session_id, [])]
                if session_id not in context_sessions or not self.conversation_context.get(session_id):
                    self.logger.info(f"📝 STRATEGIC: Using Qwen complete as fallback: '{transcript}'")
                    await self._append_to_session_context(session_id, "user", transcript)
                    
                    if session_id in self.delta_buffers:
                        buffer = self.delta_buffers[session_id]
                        if buffer and len(buffer.split()) >= 3:
                            self.logger.info(f"🎯 Final analysis for remaining buffer: '{buffer}'")
                            await self._trigger_analysis(buffer, session_id, source="qwen_final")
                        self.delta_buffers[session_id] = ""  # 清空buffer
                else:
                    self.logger.debug(f"📝 STRATEGIC: Ignoring Qwen complete, Paraformer already processed: '{transcript}'")

    async def _trigger_analysis(self, text: str, session_id: str, source: str = "qwen"):
        if session_id in self.analysis_tasks and not self.analysis_tasks[session_id].done():
            # 🎯 CORE FIX: 取消正在进行的分析，以支持更新的内容
            self.analysis_tasks[session_id].cancel()
        
        text_len = len(text)
        words_count = len(text.split())
        
        # 🎯 大幅降低分析阈值 - 支持真正的实时分析
        min_words = 5
        min_chars = 30
        
        if words_count >= min_words or text_len >= min_chars:
            self.logger.info(f"🚀 STRATEGIC: Triggering REAL-TIME analysis for {source} source (words={words_count}, chars={text_len})")
            self.analysis_tasks[session_id] = asyncio.create_task(
                self._perform_disruptive_analysis(text, session_id, False, source)
            )
        else:
            self.logger.debug(f"🤔 STRATEGIC: Text too short for analysis (words={words_count}, chars={text_len})")

    def _check_repetition(self, session_id: str, current_buffer: str) -> float:
        """检查当前发言与历史发言的相似度"""
        if session_id not in self.conversation_context:
            return 0.0
            
        history = self.conversation_context[session_id]
        if not history:
            return 0.0
            
        # 只比较最近的用户发言
        user_history = [turn['content'] for turn in history if turn['role'] == 'user']
        if not user_history:
            return 0.0
            
        # 与最近的2-3轮对话比较
        max_similarity = 0
        for past_utterance in user_history[-3:]:
            similarity = fuzz.ratio(current_buffer, past_utterance)
            if similarity > max_similarity:
                max_similarity = similarity
                
        return max_similarity

    def _get_disruptive_analysis_prompt(self) -> str:
        return """
        You are a meticulous Conversation Flow Manager. Your primary goal is to detect when a user's speech pattern becomes unproductive, specifically focusing on **semantic repetition**. Your analysis must be precise.

        Analyze the user's latest real-time utterance against the recent conversation history.

        **CRITICAL RULE FOR REPETITION:**
        An utterance is ONLY a repetition if the user is making the **exact or extremely similar or same point or asking the exact same question** that the AI has **already clearly and directly answered** in the last 2-3 turns.

        **DO NOT flag repetition if:**
        - The AI's previous answer was vague, incorrect, or dodged the question.
        - The user is rephrasing to seek clarification on the AI's answer.
        - The user is simply confirming their understanding.

        **Your analysis should follow this logic:**
        1.  Identify the core intent of the user's current speech.
        2.  Scan the all the turns of conversation history.
        3.  Did the user express this *exact same intent* before and has done this?
        4.  If so, did the AI provide a direct and sufficient answer to that previous query?
        5.  Only if the answer to Both #3 and #4 is "yes", should you consider it a repetition.

        **Example of CORRECT Repetition (Confidence > 0.8):**
        User: "What is the capital of France?"
        AI: "The capital of France is Paris."
        User: "Okay, but what is France's capital city?" -> This is a clear repetition.

        **Example of INCORRECT Repetition (Confidence = 0.0):**
        User: "What is the capital of France?"
        AI: "France is a country in Europe known for its wine and cheese."
        User: "I understand, but I asked for the capital city." -> This is a valid clarification, NOT a repetition.

        **Response Format (JSON ONLY):**
        {
          "confidence": <float, 0.0 to 1.0>,
          "reason": "<string, a precise explanation of why it is or is not a repetition, referencing the conversation history>",
          "interruption_type": "repetition",
          "suggested_response": "<string, a polite, ready-to-speak sentence to gently guide the user back on track>"
        }

        **Confidence Scale:**
        - **0.0 - 0.3:** Not a repetition. The user is clarifying, or the AI's previous answer was insufficient.
        - **0.4 - 0.7:** Possibly a repetition, but could be a nuance. Be cautious.
        - **0.8 - 1.0:** High certainty of semantic repetition where the AI has already answered clearly.

        **Suggested Response:** Should be helpful. Example: "Pardon me for interrupting, it seems we've already covered that Paris is the capital. Was there something more specific you wanted to know about it, or shall we move on to a new topic?"
        """

    async def _perform_disruptive_analysis(self, text: str, session_id: str, force_reset: bool, source: str = "qwen"):
        """Performs LLM analysis and publishes to DISRUPTIVE_ANALYSIS topic if confidence is high."""
        try:
            # 🎯 禁用简单的文本相似度检查，让LLM自己进行语义分析
            # repetition_score = self._check_repetition(session_id, text)  # <-- 注释掉这一行
            # repetition_info = ""
            # if repetition_score > 80:  # 相似度阈值，可以调整
            #     self.logger.info(f"🔥 High repetition detected for session {session_id}: {repetition_score}%")
            #     repetition_info = f"\n\nREPETITION DETECTED: Current speech is {repetition_score}% similar to recent utterances."
            
            context = self.conversation_context.get(session_id, [])
            context_summary = "\n".join([f"{msg['role']}: {msg['content']}" for msg in context])
            # 🎯 移除repetition_info的添加
            prompt_content = f"Conversation Context:\n{context_summary}\n\nUser's real-time speech:\n{text}"

            response = await self.client.chat.completions.create(
                model=settings.strategic_thinker_model,
                messages=[
                    {"role": "system", "content": self._get_disruptive_analysis_prompt()},
                    {"role": "user", "content": prompt_content}
                ],
                temperature=0.1, max_tokens=200
            )

            analysis = json.loads(response.choices[0].message.content)
            confidence = analysis.get("confidence", 0.0)
            reason = analysis.get("reason", "")
            
            # 🎯 新增调试日志
            self.logger.info(f"🧠 Disruptive analysis result for {session_id}: confidence={confidence:.2f}, reason='{reason}'")

            if confidence > self.disruptive_threshold:
                await message_bus.publish(TopicNames.DISRUPTIVE_ANALYSIS, create_message(
                    event_type=EventType.STRATEGIC_ANALYSIS,
                    message_type=MessageType.STRATEGIC_CONTENT,
                    data={"session_id": session_id, "analysis": analysis},
                    source_agent=self.agent_id
                ))
                self.delta_buffers[session_id] = "" # Reset buffer
        except Exception as e:
            # 🎯 修复日志语法 - 使用标准logging的exception方法
            self.logger.exception(
                f"Disruptive analysis failed for session {session_id}: {e}"
            )
        finally:
            if force_reset and session_id in self.delta_buffers:
                self.delta_buffers[session_id] = ""
            if session_id in self.analysis_tasks:
                del self.analysis_tasks[session_id] 