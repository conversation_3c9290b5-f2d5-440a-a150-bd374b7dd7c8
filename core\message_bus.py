"""
Message Bus implementation using asyncio for the Audio Agent system.
Central communication hub for the Dispider architecture.
Lightweight in-memory implementation without external dependencies.
"""

import asyncio
import json
import uuid
from typing import Dict, Callable, Any, Optional, List
from datetime import datetime
from loguru import logger

from .event_types import EventType, MessageType, BaseMessage


class TopicNames:
    """Centralized topic names for the message bus."""
    
    # Audio and Speech Topics
    AUDIO_INPUT = "audio.input"
    AUDIO_OUTPUT = "audio.output"
    TRANSCRIPT_DELTA = "transcript.delta"
    TRANSCRIPT_COMPLETED = "transcript.completed"
    SPEECH_STARTED = "speech.started"
    SPEECH_STOPPED = "speech.stopped"
    SPEECH_COMMAND = "speech.command"
    SPEECH_STATUS = "speech.status"
    SPEECH_EVENTS = "speech.events"
    
    # 🎯 **新增：** 明确区分用户和AI的语音事件
    USER_SPEECH_STARTED = "events.user.speech.started"
    USER_SPEECH_ENDED = "events.user.speech.ended"
    AI_SPEECH_STARTED = "events.ai.speech.started"
    AI_SPEECH_ENDED = "events.ai.speech.ended"
    
    # Enhanced Speech and Audio Control Topics
    SPEECH_DETECTION = "speech.detection"  # 语音检测事件
    SPEECH_INTERRUPTION = "speech.interruption"  # 语音中断事件
    AUDIO_PLAYBACK = "audio.playback"  # 音频播放控制
    AUDIO_QUEUE = "audio.queue"  # 音频队列管理
    
    # 科大讯飞ASR主题
    XUNFEI_TRANSCRIPT_DELTA = "events.xunfei.transcript.delta"
    XUNFEI_TRANSCRIPT_COMPLETED = "events.xunfei.transcript.completed"
    XUNFEI_CONNECTION_STATUS = "events.xunfei.connection.status"
    
    # 双轨ASR控制主题
    DUAL_ASR_CONTROL = "events.dual_asr.control"
    ASR_SERVICE_SWITCH = "events.asr.service.switch"
    
    # Text-to-Speech Topics
    TTS_COMMANDS = "tts.commands"
    
    # Analysis Topics
    TACTICAL_ANALYSIS = "analysis.tactical"
    STRATEGIC_ANALYSIS = "analysis.strategic"
    TACTICAL_ANALYSIS_REQUEST = "analysis.tactical.request"
    STRATEGIC_ANALYSIS_REQUEST = "analysis.strategic.request"
    
    # Decision and Interruption Topics
    INTERRUPTION_DECISION = "interruption.decision"
    INTERRUPTION_COMMAND = "interruption.command"
    RESPONSE_REQUEST = "response.request"
    RESPONSE_GENERATED = "response.generated"
    RESPONSE_EVENTS = "response.events"
    
    # Connection and Quality Management Topics
    CONNECTION_EVENTS = "connection.events"  # 连接事件
    CONNECTION_QUALITY = "connection.quality"  # 连接质量
    HEARTBEAT = "connection.heartbeat"  # 心跳检测
    
    # Session and State Topics
    SESSION_CREATED = "session.created"
    SESSION_ENDED = "session.ended"
    SESSION_EVENTS = "session.events"
    
    # Error and System Monitoring Topics  
    ERRORS = "system.errors"  # 系统错误事件
    SYSTEM_MONITORING = "system.monitoring"  # 系统监控
    HEALTH_CHECK = "system.health"  # 健康检查
    CONVERSATION_STATE = "conversation.state"
    AGENT_STATUS = "agent.status"
    
    # 🎯 新增：系统命令主题（用于音频护盾控制）
    SYSTEM_COMMANDS = "system.commands"
    
    # Interruption Analysis Topics (Separated by Type)
    DISRUPTIVE_ANALYSIS = "analysis.disruptive"
    
    # AI Transcript Events (for Strategic Thinker integration)
    AI_TRANSCRIPT_EVENTS = "ai.transcript.events"
    
    # UI-specific updates that don't involve the orchestrator/speaker
    UI_UPDATE = "ui.update"

    # Mode Selection Topics
    MODE_SELECTION = "mode.selection"
    
    # Language Detection Topics
    LANGUAGE_DETECTION = "language.detection"


def create_message(event_type: EventType, message_type: MessageType, data: Dict[str, Any], source_agent: Optional[str] = None) -> Dict[str, Any]:
    """Create a standardized message for the message bus."""
    message = {
        "message_id": str(uuid.uuid4()),
        "event_type": event_type.value,
        "message_type": message_type.value,
        "data": data,
        "timestamp": datetime.now().isoformat()
    }
    
    if source_agent:
        message["source_agent"] = source_agent
        
    return message


class MessageBus:
    """
    Central message bus for inter-agent communication using asyncio.
    Implements the communication backbone for the Dispider architecture.
    Lightweight in-memory implementation without external dependencies.
    """
    
    def __init__(self):
        # 主题到队列的映射
        self.topic_queues: Dict[str, asyncio.Queue] = {}
        # 订阅者回调管理
        self.subscribers: Dict[str, List[Callable]] = {}
        # 消息分发任务管理
        self.dispatch_tasks: Dict[str, asyncio.Task] = {}
        # 运行状态
        self.is_running = False
        # 队列最大大小，防止内存溢出
        self.max_queue_size = 1000
        
    async def initialize(self):
        """Initialize the message bus."""
        try:
            self.is_running = True
            logger.info("Message bus initialized successfully (asyncio mode)")
            
        except Exception as e:
            logger.error(f"Failed to initialize message bus: {e}")
            raise
            
    async def connect(self) -> bool:
        """Connect to the message bus (compatibility method)."""
        try:
            # In asyncio mode, no external connection needed
            # Just ensure we're initialized
            if not self.is_running:
                await self.initialize()
            logger.debug("Message bus connected (asyncio mode)")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to message bus: {e}")
            return False
            
    async def start_listening(self):
        """Start listening for messages (compatibility method)."""
        try:
            # In asyncio mode, listening starts automatically when subscribing
            # This is a no-op for compatibility
            logger.debug("Message bus listening started (asyncio mode)")
        except Exception as e:
            logger.error(f"Failed to start listening: {e}")
            
    async def stop_listening(self):
        """Stop listening for messages (compatibility method)."""
        try:
            # In asyncio mode, this is handled by shutdown
            # This is a no-op for compatibility with agent interfaces
            logger.debug("Message bus listening stopped (asyncio mode)")
        except Exception as e:
            logger.error(f"Failed to stop listening: {e}")
            
    async def disconnect(self):
        """Disconnect from the message bus (compatibility method)."""
        try:
            # In asyncio mode, this is equivalent to partial shutdown
            # Don't fully shutdown as other agents might still be using it
            logger.debug("Message bus disconnected (asyncio mode)")
        except Exception as e:
            logger.error(f"Failed to disconnect from message bus: {e}")
            
    async def shutdown(self):
        """Shutdown the message bus."""
        try:
            self.is_running = False
            
            # 取消所有分发任务
            for task in self.dispatch_tasks.values():
                if not task.done():
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass
                        
            # 清理资源
            self.topic_queues.clear()
            self.subscribers.clear()
            self.dispatch_tasks.clear()
                
            logger.info("Message bus shutdown complete")
            
        except Exception as e:
            logger.error(f"Error during message bus shutdown: {e}")
            
    async def subscribe(self, topic: str, callback: Callable):
        """Subscribe to a topic with a callback function."""
        try:
            if topic not in self.subscribers:
                self.subscribers[topic] = []
                # 创建主题队列
                self.topic_queues[topic] = asyncio.Queue(maxsize=self.max_queue_size)
                # 启动消息分发任务
                self.dispatch_tasks[topic] = asyncio.create_task(
                    self._message_dispatcher(topic)
                )
                    
            self.subscribers[topic].append(callback)
            logger.debug(f"Subscribed to topic: {topic}")
            
        except Exception as e:
            logger.error(f"Error subscribing to topic {topic}: {e}")
            
    async def unsubscribe(self, topic: str, callback: Callable):
        """Unsubscribe from a topic."""
        try:
            if topic in self.subscribers:
                if callback in self.subscribers[topic]:
                    self.subscribers[topic].remove(callback)
                    
                # 如果没有更多订阅者，清理资源
                if not self.subscribers[topic]:
                    del self.subscribers[topic]
                    
                    # 取消分发任务
                    if topic in self.dispatch_tasks:
                        task = self.dispatch_tasks[topic]
                        if not task.done():
                            task.cancel()
                            try:
                                await task
                            except asyncio.CancelledError:
                                pass
                        del self.dispatch_tasks[topic]
                        
                    # 清理队列
                    if topic in self.topic_queues:
                        del self.topic_queues[topic]
                        
            logger.debug(f"Unsubscribed from topic: {topic}")
            
        except Exception as e:
            logger.error(f"Error unsubscribing from topic {topic}: {e}")
            
    async def publish(self, topic: str, message: Dict[str, Any]):
        """
        Publish a message to a topic.
        
        Args:
            topic: Topic name
            message: Message to publish
        """
        if not self.is_running:
            logger.warning("Message bus not running, cannot publish")
            return
        
        # Generate message ID for tracking
        message_id = str(uuid.uuid4())
        message["message_id"] = message_id
        message["timestamp"] = datetime.now().isoformat()
        
        # Check if topic has subscribers
        if topic not in self.subscribers or not self.subscribers[topic]:
            # Log critical topics with no subscribers as warnings
            critical_topics = [
                TopicNames.TRANSCRIPT_COMPLETED,
                TopicNames.TACTICAL_ANALYSIS, 
                TopicNames.STRATEGIC_ANALYSIS,
                TopicNames.INTERRUPTION_DECISION
            ]
            
            if topic in critical_topics:
                logger.warning(f"Critical topic '{topic}' has no subscribers, message will be dropped: {message.get('event_type', 'unknown')}")
            else:
                logger.debug(f"No subscribers for topic {topic}, dropping message")
            return
            
        # 🎯 新增日志: 确认消息即将进入队列
        logger.info(f"📬 MESSAGEBUS: Queuing message for topic '{topic}'. Subscribers: {len(self.subscribers.get(topic, []))}. Message type: {message.get('event_type')}")
        
        logger.debug(f"Published message to {topic}: {message_id}")
        
        # Add message to topic queue
        if topic not in self.topic_queues:
            self.topic_queues[topic] = asyncio.Queue()
        
        await self.topic_queues[topic].put(message)
            
    async def _message_dispatcher(self, topic: str):
        """后台消息分发器，从队列中取出消息并分发给订阅者."""
        try:
            queue = self.topic_queues[topic]
            
            while self.is_running:
                try:
                    # 从队列中获取消息，设置超时避免无限等待
                    message = await asyncio.wait_for(queue.get(), timeout=1.0)
                    
                    # 分发给所有订阅者
                    await self._dispatch_message(topic, message)
                    
                    # 标记任务完成
                    queue.task_done()
                    
                except asyncio.TimeoutError:
                    # 超时是正常的，继续监听
                    continue
                except Exception as e:
                    logger.error(f"Error in message dispatcher for topic {topic}: {e}")
                    
        except Exception as e:
            logger.error(f"Message dispatcher error for topic {topic}: {e}")
            
    async def _dispatch_message(self, topic: str, message: Dict[str, Any]):
        """Dispatch message to all subscribers of a topic."""
        try:
            if topic in self.subscribers:
                # 🎯 新增日志: 确认即将调用回调
                logger.info(f"📨 MESSAGEBUS: Dispatching message on topic '{topic}' to {len(self.subscribers[topic])} subscribers.")
                
                # 调用所有订阅者的回调函数
                tasks = []
                for i, callback in enumerate(self.subscribers[topic]):
                    # 🎯 新增日志: 确认正在为哪个回调创建任务
                    logger.info(f"  -> Creating task for subscriber #{i+1} ({getattr(callback, '__qualname__', 'unknown')})")
                    try:
                        task = asyncio.create_task(callback(topic, message))
                        tasks.append(task)
                    except Exception as e:
                        logger.error(f"Error creating callback task for topic {topic}: {e}")
                    
                # 等待所有回调完成，捕获异常但不中断其他回调
                if tasks:
                    results = await asyncio.gather(*tasks, return_exceptions=True)
                    
                    # 记录回调中的异常
                    for i, result in enumerate(results):
                        if isinstance(result, Exception):
                            # 🎯 新增日志: 捕获并记录回调函数内部的异常
                            callback_name = getattr(self.subscribers[topic][i], '__qualname__', 'unknown')
                            logger.error(f"💥 MESSAGEBUS: Exception in callback '{callback_name}' for topic '{topic}': {result}", exc_info=True)
                    
        except Exception as e:
            logger.error(f"Error dispatching message for topic {topic}: {e}")
            
    async def get_status(self) -> Dict[str, Any]:
        """Get message bus status and statistics."""
        subscription_stats = {}
        for topic, callbacks in self.subscribers.items():
            subscription_stats[topic] = {
                "subscriber_count": len(callbacks),
                "queue_size": self.topic_queues[topic].qsize() if topic in self.topic_queues else 0
            }
        
        # Check for critical topics without subscribers
        critical_topics = [
            TopicNames.TRANSCRIPT_COMPLETED,
            TopicNames.TACTICAL_ANALYSIS, 
            TopicNames.STRATEGIC_ANALYSIS,
            TopicNames.INTERRUPTION_DECISION,
            TopicNames.SPEECH_EVENTS
        ]
        
        missing_subscribers = []
        for topic in critical_topics:
            if topic not in self.subscribers or not self.subscribers[topic]:
                missing_subscribers.append(topic)
        
        return {
            "is_running": self.is_running,
            "total_topics": len(self.subscribers),
            "subscription_stats": subscription_stats,
            "missing_critical_subscribers": missing_subscribers,
            "health_status": "unhealthy" if missing_subscribers else "healthy"
        }


# Global message bus instance
message_bus = MessageBus() 