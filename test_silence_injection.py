#!/usr/bin/env python3
"""
空音频注入方案测试脚本

测试Strategic Thinker介入时通过注入空音频来阻止用户打断AI回应的新方案。
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Any

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MockSession:
    """模拟会话对象"""
    def __init__(self, session_id: str):
        self.session_id = session_id
        self.audio_shield_active = False
        self.audio_shield_reason = None
        self.state = "IDLE"

class MockRealtimeManager:
    """模拟RealtimeManager"""
    def __init__(self):
        self.silence_injected = False
        self.buffer_cleared = False
        self.injected_duration = 0.0
        
    async def inject_silence_audio(self, duration_seconds: float = 5.0) -> bool:
        """注入空音频"""
        self.silence_injected = True
        self.injected_duration = duration_seconds
        logger.info(f"🔇 Mock injected {duration_seconds}s silence audio")
        return True
        
    async def clear_audio_buffer(self) -> bool:
        """清空音频缓冲区"""
        self.buffer_cleared = True
        self.silence_injected = False  # 清空后重置状态
        logger.info("🗑️ Mock audio buffer cleared")
        return True

class SilenceInjectionTest:
    """空音频注入测试类"""
    
    def __init__(self):
        self.session = MockSession("test_session_001")
        self.realtime_manager = MockRealtimeManager()
        
    async def simulate_strategic_interruption(self):
        """模拟Strategic Thinker触发的AI主动打断"""
        logger.info("🧠 Strategic Thinker detected repetition, triggering interruption...")
        
        # 1. 激活音频护盾
        await self.activate_audio_shield("ai_interruption_repetition")
        
        # 2. 注入5秒空音频
        await self.realtime_manager.inject_silence_audio(duration_seconds=5.0)
        
        # 3. 模拟AI开始说话
        logger.info("🎤 AI starts speaking: 'I notice you're repeating the same question...'")
        
        return True
        
    async def activate_audio_shield(self, reason: str):
        """激活音频护盾"""
        self.session.audio_shield_active = True
        self.session.audio_shield_reason = reason
        logger.info(f"🛡️ Audio shield activated: {reason}")
        
    async def simulate_user_barge_in_attempt(self):
        """模拟用户尝试打断AI"""
        logger.info("👤 User attempts to speak during AI interruption...")
        
        # 检查音频护盾状态
        if self.session.audio_shield_active:
            logger.info(f"🛡️ User speech BLOCKED by audio shield: {self.session.audio_shield_reason}")
            return False
        else:
            logger.info("⚡️ User speech ALLOWED - barge-in executed")
            return True
            
    async def simulate_audio_processing(self):
        """模拟音频处理"""
        logger.info("🎤 Processing user audio...")
        
        # 检查是否有空音频占位
        if self.realtime_manager.silence_injected:
            logger.info("🔇 User audio BLOCKED - silence audio is occupying the buffer")
            return False
        else:
            logger.info("✅ User audio PROCESSED normally")
            return True
            
    async def deactivate_audio_shield(self):
        """停用音频护盾"""
        self.session.audio_shield_active = False
        self.session.audio_shield_reason = None
        await self.realtime_manager.clear_audio_buffer()
        logger.info("✅ Audio shield deactivated, buffer cleared")
        
    async def run_test_scenario(self):
        """运行完整的测试场景"""
        logger.info("=" * 60)
        logger.info("🧪 SILENCE INJECTION TEST SCENARIO")
        logger.info("=" * 60)
        
        # 场景1: AI主动打断前的状态
        logger.info("\n📍 Phase 1: Normal conversation state")
        user_can_interrupt = await self.simulate_user_barge_in_attempt()
        audio_processed = await self.simulate_audio_processing()
        
        assert user_can_interrupt == True, "User should be able to interrupt in normal state"
        assert audio_processed == True, "Audio should be processed in normal state"
        logger.info("✅ Phase 1 passed: Normal interruption works")
        
        # 场景2: Strategic Thinker触发AI主动打断
        logger.info("\n📍 Phase 2: Strategic Thinker triggers AI interruption")
        await self.simulate_strategic_interruption()
        
        # 验证空音频注入状态
        assert self.realtime_manager.silence_injected == True, "Silence audio should be injected"
        assert self.realtime_manager.injected_duration == 5.0, "Should inject 5 seconds of silence"
        logger.info("✅ Phase 2 passed: Silence audio injected successfully")
        
        # 场景3: 用户尝试打断AI（应该被阻止）
        logger.info("\n📍 Phase 3: User attempts to interrupt AI (should be blocked)")
        user_can_interrupt = await self.simulate_user_barge_in_attempt()
        audio_processed = await self.simulate_audio_processing()
        
        assert user_can_interrupt == False, "User should NOT be able to interrupt during audio shield"
        assert audio_processed == False, "Audio should NOT be processed when silence is injected"
        logger.info("✅ Phase 3 passed: Silence injection blocks user interruption")
        
        # 场景4: 保护期结束，恢复正常状态
        logger.info("\n📍 Phase 4: Protection period ends, restore normal state")
        await asyncio.sleep(1)  # 模拟保护期延迟
        await self.deactivate_audio_shield()
        
        # 验证缓冲区清空状态
        assert self.realtime_manager.buffer_cleared == True, "Audio buffer should be cleared"
        assert self.realtime_manager.silence_injected == False, "Silence injection should be reset"
        logger.info("✅ Phase 4 passed: Audio buffer cleared successfully")
        
        # 场景5: 验证恢复后用户可以正常打断
        logger.info("\n📍 Phase 5: Verify normal interruption is restored")
        user_can_interrupt = await self.simulate_user_barge_in_attempt()
        audio_processed = await self.simulate_audio_processing()
        
        assert user_can_interrupt == True, "User should be able to interrupt after shield deactivation"
        assert audio_processed == True, "Audio should be processed after buffer clear"
        logger.info("✅ Phase 5 passed: Normal interruption restored")
        
        logger.info("\n" + "=" * 60)
        logger.info("🎉 ALL TESTS PASSED! Silence injection mechanism works correctly.")
        logger.info("=" * 60)
        
        # 总结新方案的优势
        logger.info("\n🎯 NEW APPROACH ADVANTAGES:")
        logger.info("  ✅ Physical layer blocking - silence audio occupies the buffer")
        logger.info("  ✅ No complex VAD state management")
        logger.info("  ✅ Automatic recovery through buffer clearing")
        logger.info("  ✅ Maintains connection stability")
        logger.info("  ✅ Simple and reliable implementation")

async def main():
    """主测试函数"""
    test = SilenceInjectionTest()
    try:
        await test.run_test_scenario()
    except AssertionError as e:
        logger.error(f"❌ TEST FAILED: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ TEST ERROR: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
